<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Preference</title>
    <script nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <script type="module" crossorigin src="./assets/preference-DeKVSMEx.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-kH3m-zOb.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-C8Qb_O9b.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/CalloutAugment-BUpCn_TI.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-BAmr_-U4.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/async-messaging-CCLqHBoR.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/message-broker-CwcPcQ_e.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/types-CGlLNakm.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/file-paths-Bl_IgYJd.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/BaseTextInput-TeF8u93x.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-BnlWKkvq.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/folder-opened-D9klsFkp.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-D-fDrvnq.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/svelte-component-B6upIdsM.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/Filespan-B9x3U15q.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/preload-helper-Dv6uf1Os.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/toggleHighContrast-Cb9MCs64.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-B_qLJJaQ.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/diff-operations-DJ1OAK7V.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/keypress-DD1aQVr0.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/await-BH7XNauH.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/chat-context-nrEZUHNl.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-C4gKbsWy.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/remote-agents-client-TVS4i5h_.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/ra-diff-ops-model-dZccOtNT.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-BZfc2Zk1.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/TextAreaAugment-IsRYkfgU.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/OpenFileButton-CgBtRJfx.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/CollapseButtonAugment-DNisU6cu.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/user-DigCn7eq.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/MaterialIcon-Cs9y5rDt.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/CopyButton-BDqSrh0t.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/ellipsis-2hnmWCsv.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/LanguageIcon-Bn3MfWRN.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/next-edit-types-904A5ehg.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/IconFilePath-C18xEJF3.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/chevron-down-BbSBSK7f.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-XEt2J8A6.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/augment-logo-C7lllHv9.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/pen-to-square-CYPFKWo4.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/check-C8b2LRem.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/AugmentMessage-CsMcb4z_.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DoxdFmoV.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-B4afvB2A.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/AugmentMessage-Csj4x_Cn.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/BaseTextInput-CEzLOEg8.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/folder-opened-hTsrGIsd.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/index-BlHvDt2c.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/CalloutAugment-C-z-uXWx.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-DRIZURB3.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/index-D9au8v71.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/diff-operations-MyOXHVsl.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/Filespan-CMEPAZfs.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/toggleHighContrast-D4zjdeIP.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/OpenFileButton-bH4F3VXH.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/TextAreaAugment-k8sG2hbx.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-BcSV_kHI.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/CollapseButtonAugment-BX1Qki-o.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/user-dXUx3CYB.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/MaterialIcon-BO_oU5T3.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/CopyButton-B0_wR17F.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/IconFilePath-BVaLv7mP.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/LanguageIcon-D78BqCXT.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/preference-CUbmjS6T.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
