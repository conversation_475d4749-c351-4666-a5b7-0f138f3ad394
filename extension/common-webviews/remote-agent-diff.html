<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Remote Agent Diff</title>
    <script nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <script type="module" crossorigin src="./assets/remote-agent-diff-DLy2p2zZ.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-kH3m-zOb.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-IgXUmngh.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-C8Qb_O9b.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/async-messaging-CCLqHBoR.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/message-broker-CwcPcQ_e.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/ra-diff-ops-model-dZccOtNT.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-BAmr_-U4.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/ModalAugment-C998l0mA.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-XEt2J8A6.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/svelte-component-B6upIdsM.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/file-paths-Bl_IgYJd.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/Filespan-B9x3U15q.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/preload-helper-Dv6uf1Os.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/toggleHighContrast-Cb9MCs64.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-D-fDrvnq.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-B_qLJJaQ.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/diff-operations-DJ1OAK7V.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/CollapseButtonAugment-DNisU6cu.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/VSCodeCodicon-CIbfiFym.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-BZfc2Zk1.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/MaterialIcon-Cs9y5rDt.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-C4gKbsWy.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/LanguageIcon-Bn3MfWRN.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/exclamation-triangle-C_LrR9gu.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DoxdFmoV.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-DgOX1UWm.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-B4afvB2A.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/ModalAugment-CM3byOYD.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/diff-operations-MyOXHVsl.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/CollapseButtonAugment-BX1Qki-o.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/index-BlHvDt2c.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/VSCodeCodicon-DVaocTud.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-DRIZURB3.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-BcSV_kHI.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/MaterialIcon-BO_oU5T3.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/LanguageIcon-D78BqCXT.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/Filespan-CMEPAZfs.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/toggleHighContrast-D4zjdeIP.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/remote-agent-diff-Bzk3Sgw8.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/remote-agent-panel-base-CVwMZATI.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
