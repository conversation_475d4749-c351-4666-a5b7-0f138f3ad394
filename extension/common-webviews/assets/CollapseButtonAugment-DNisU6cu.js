import{l as b,f as q,a as G,t as o,b as p,v as $e,u as ye,z as be,A as ce,B as I,w as oe,aC as we,C as y,I as xe,J as ie,K as E,R as U,O as V,Y as ke,_ as _e,$ as Y,a5 as P,H as Q,m as le,a6 as Ce,S as re,W as de,Q as ne,G as T,D as R,N as ve,P as Se,a4 as ze,V as Le}from"./SpinnerAugment-kH3m-zOb.js";import{h as J,a as He,I as Ie}from"./IconButtonAugment-C8Qb_O9b.js";import{b as Te}from"./CardAugment-BAmr_-U4.js";var Be=q("<svg><!></svg>");function Ye(c,e){const i=b(e,["children","$$slots","$$events","$$legacy"]);var s=Be();G(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...i}));var l=o(s);J(l,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M248.4 84.3c1.6-2.7 4.5-4.3 7.6-4.3s6 1.6 7.6 4.3L461.9 410c1.4 2.3 2.1 4.9 2.1 7.5 0 8-6.5 14.5-14.5 14.5h-387c-8 0-14.5-6.5-14.5-14.5 0-2.7.7-5.3 2.1-7.5zm-41-25L9.1 385c-6 9.8-9.1 21-9.1 32.5C0 452 28 480 62.5 480h387c34.5 0 62.5-28 62.5-62.5 0-11.5-3.2-22.7-9.1-32.5L304.6 59.3C294.3 42.4 275.9 32 256 32s-38.3 10.4-48.6 27.3M288 368a32 32 0 1 0-64 0 32 32 0 1 0 64 0m-8-184c0-13.3-10.7-24-24-24s-24 10.7-24 24v96c0 13.3 10.7 24 24 24s24-10.7 24-24z"/>',!0),p(c,s)}var Me=q("<svg><!></svg>");function Fe(c,e){const i=b(e,["children","$$slots","$$events","$$legacy"]);var s=Me();G(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 448 512",...i}));var l=o(s);J(l,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M239 498.7c-8.8 7-21.2 7-30 0l-160-128c-10.4-8.3-12-23.4-3.7-33.7s23.4-12 33.7-3.8l145 116 145-116c10.3-8.3 25.5-6.6 33.7 3.8s6.6 25.5-3.7 33.7zm160-357.4c10.4 8.3 12 23.4 3.8 33.7s-23.4 12-33.7 3.7L224 62.7l-145 116c-10.4 8.3-25.5 6.6-33.7-3.7s-6.6-25.5 3.7-33.7l160-128c8.8-7 21.2-7 30 0z"/>',!0),p(c,s)}const pe=Symbol("collapsible");function Ae(){return ye(pe)}var Pe=E('<footer class="c-collapsible__footer svelte-gbhym3"><!></footer>'),Ue=E('<div class="c-collapsible__body svelte-gbhym3"><!></div> <!>',1),Ee=E('<div><header><div><!></div></header> <div><div class="c-collapsible__content-inner svelte-gbhym3"><!></div></div></div>');function qe(c,e){const i=be(e),s=b(e,["children","$$slots","$$events","$$legacy"]),l=b(s,["toggle","collapsed","stickyHeader","expandable","isHeaderStuck","stickyHeaderTop"]);ce(e,!1);const[B,C]=de(),d=()=>Y(v,"$collapsedStore",B),w=()=>Y(L,"$expandableStore",B),S=le();let u=I(e,"collapsed",12,!1),M=I(e,"stickyHeader",8,!1),r=I(e,"expandable",12,!0),z=I(e,"isHeaderStuck",12,!1),x=I(e,"stickyHeaderTop",24,()=>-.5);const v=oe(u()),N=we(v,a=>a),L=oe(r());let n,O=le(!1);function K(a){r()?v.set(a):v.set(!0)}const D=function(){K(!d())};$e(pe,{collapsed:N,setCollapsed:K,toggle:D,expandable:L}),y(()=>w(),()=>{r(w())}),y(()=>T(r()),()=>{L.set(r())}),y(()=>d(),()=>{u(d())}),y(()=>T(u()),()=>{v.set(u())}),y(()=>T(r()),()=>{r()||v.set(!0)}),y(()=>T(u()),()=>{u()?(clearTimeout(n),n=setTimeout(()=>{R(O,!1)},200)):(clearTimeout(n),R(O,!0))}),y(()=>(Q(S),T(l)),()=>{R(S,l.class)}),xe(),ie();var F=Ee();let W,j;var A=o(F);let X;var Z=o(A);let ee;var ue=o(Z);U(ue,e,"header",{},null),He(A,(a,k)=>function(h,m){const{onStuck:g,onUnstuck:H,offset:f=0}=m,t=document.createElement("div");t.style.position="absolute",t.style.top=f?`${f}px`:"0",t.style.height="1px",t.style.width="100%",t.style.pointerEvents="none",t.style.opacity="0",t.style.zIndex="-1";const _=h.parentNode;if(!_)return{update:()=>{},destroy:()=>{}};window.getComputedStyle(_).position==="static"&&(_.style.position="relative"),_.insertBefore(t,h);const ae=new IntersectionObserver(([$])=>{$.isIntersecting?H==null||H():g==null||g()},{threshold:0,rootMargin:"-1px 0px 0px 0px"});return ae.observe(t),{update($){m.onStuck=$.onStuck,m.onUnstuck=$.onUnstuck,$.offset!==void 0&&$.offset!==f&&(t.style.top=`${$.offset}px`)},destroy(){ae.disconnect(),t.remove()}}}(a,k),()=>({offset:-x(),onStuck:()=>{z(!0)},onUnstuck:()=>{z(!1)}}));var se=ne(A,2);let te;var he=o(se),me=o(he),ge=a=>{var k=Ue(),h=ve(k),m=o(h);U(m,e,"default",{},null);var g=ne(h,2),H=f=>{var t=Pe(),_=o(t);U(_,e,"footer",{},null),p(f,t)};V(g,f=>{Se(()=>i.footer)&&f(H)}),p(a,k)};V(me,a=>{w()&&Q(O)&&a(ge)}),ke((a,k,h,m,g)=>{W=P(F,1,`c-collapsible ${Q(S)??""}`,"svelte-gbhym3",W,a),j=Ce(F,"",j,k),X=P(A,1,"c-collapsible__header svelte-gbhym3",null,X,h),ee=P(Z,1,"c-collapsible__header-inner svelte-gbhym3",null,ee,m),te=P(se,1,"c-collapsible__content svelte-gbhym3",null,te,g)},[()=>({"is-collapsed":d(),"is-expandable":w()}),()=>({"--sticky-header-top":`${x()}px`}),()=>({"is-sticky":M()}),()=>({"is-collapsed":d(),"is-header-stuck":z(),"has-header-padding":x()>0}),()=>({"is-collapsed":d()})],_e),p(c,F),Te(e,"toggle",D);var fe=re({toggle:D});return C(),fe}var Ne=q("<svg><!></svg>");function Oe(c,e){const i=b(e,["children","$$slots","$$events","$$legacy"]);var s=Ne();G(s,()=>({xmlns:"http://www.w3.org/2000/svg",width:"8",height:"10","data-ds-icon":"fa",viewBox:"0 0 8 10",...i}));var l=o(s);J(l,()=>'<path d="M4.451 6.357a.42.42 0 0 0-.527 0L1.11 8.607a.42.42 0 0 0-.065.592.424.424 0 0 0 .593.067l2.548-2.04 2.55 2.04a.423.423 0 0 0 .527-.66zm2.813-4.965a.422.422 0 1 0-.526-.658l-2.55 2.04-2.55-2.04a.421.421 0 1 0-.527.658l2.813 2.25a.42.42 0 0 0 .527 0z"/>',!0),p(c,s)}var De=E('<span class="c-collapse-button-augment__icon svelte-hw7s17"><!></span>');function Ge(c,e){const i=b(e,["children","$$slots","$$events","$$legacy"]),s=b(i,[]);ce(e,!1);const[l,B]=de(),C=()=>Y(d,"$collapsed",l),{collapsed:d,setCollapsed:w}=Ae();ie(),Ie(c,ze({variant:"ghost-block",color:"neutral",size:1},()=>s,{$$events:{click:function(){w(!C())}},children:(S,u)=>{var M=De(),r=o(M);U(r,e,"default",{get collapsed(){return C()}},z=>{var x=Le(),v=ve(x),N=n=>{Fe(n,{})},L=n=>{Oe(n,{})};V(v,n=>{C()?n(N):n(L,!1)}),p(z,x)}),p(S,M)},$$slots:{default:!0}})),re(),B()}export{Fe as A,qe as C,Ye as T,Ge as a,Oe as b,Ae as g};
