import{l as G,A as at,B as i,m as g,C as N,I as st,J as lt,K as I,V as P,N as M,O as X,b as $,D as c,H as s,_ as vt,a4 as tt,R,Q as J,t as F,S as nt,P as it,G as O,F as rt,z as ht,al as dt,a as ft,M as pt,an as gt,ar as p,ab as m,Y as $t,a7 as mt,Z as yt}from"./SpinnerAugment-kH3m-zOb.js";import{I as bt,b as l,a as wt}from"./IconButtonAugment-C8Qb_O9b.js";import{T as xt,a as et}from"./CardAugment-BAmr_-U4.js";import{B as kt}from"./ButtonAugment-BZfc2Zk1.js";import{B as Ct,b as Tt}from"./BaseTextInput-TeF8u93x.js";var Lt=I("<!> <!> <!>",1),Rt=I('<div class="c-successful-button svelte-1dvyzw2"><!></div>');function Nt(K,t){var a;const Q=G(t,["children","$$slots","$$events","$$legacy"]),A=G(Q,["defaultColor","tooltip","stateVariant","onClick","tooltipDuration","icon","stickyColor","persistOnTooltipClose","tooltipNested"]);at(t,!1);const y=g(),b=g(),T=g();let w,x=i(t,"defaultColor",8),B=i(t,"tooltip",24,()=>{}),S=i(t,"stateVariant",24,()=>{}),Z=i(t,"onClick",8),_=i(t,"tooltipDuration",8,1500),u=i(t,"icon",8,!1),V=i(t,"stickyColor",8,!0),j=i(t,"persistOnTooltipClose",8,!1),Y=i(t,"tooltipNested",24,()=>{}),r=g("neutral"),k=g(x()),E=g(void 0),D=g((a=B())==null?void 0:a.neutral);async function q(o){var h;try{c(r,await Z()(o)??"neutral")}catch{c(r,"failure")}c(D,(h=B())==null?void 0:h[s(r)]),clearTimeout(w),w=setTimeout(()=>{var f;(f=s(E))==null||f(),V()||c(r,"neutral")},_())}N(()=>(s(y),s(b),O(A)),()=>{c(y,A.variant),c(b,rt(A,["variant"]))}),N(()=>(O(S()),s(r),s(y)),()=>{var o;c(T,((o=S())==null?void 0:o[s(r)])??s(y))}),N(()=>(s(r),O(x())),()=>{s(r)==="success"?c(k,"success"):s(r)==="failure"?c(k,"error"):c(k,x())}),st(),lt();var v=Rt(),L=F(v);const n=vt(()=>(O(et),it(()=>[et.Hover])));xt(L,{onOpenChange:function(o){var h;j()||o||(clearTimeout(w),w=void 0,c(D,(h=B())==null?void 0:h.neutral),V()||c(r,"neutral"))},get content(){return s(D)},get triggerOn(){return s(n)},get nested(){return Y()},get requestClose(){return s(E)},set requestClose(o){c(E,o)},children:(o,h)=>{var f=P(),U=M(f),ct=H=>{bt(H,tt(()=>s(b),{get color(){return s(k)},get variant(){return s(T)},$$events:{click:q,keyup(e){l.call(this,t,e)},keydown(e){l.call(this,t,e)},mousedown(e){l.call(this,t,e)},mouseover(e){l.call(this,t,e)},focus(e){l.call(this,t,e)},mouseleave(e){l.call(this,t,e)},blur(e){l.call(this,t,e)},contextmenu(e){l.call(this,t,e)}},children:(e,W)=>{var d=Lt(),C=M(d);R(C,t,"iconLeft",{},null);var z=J(C,2);R(z,t,"default",{},null);var ut=J(z,2);R(ut,t,"iconRight",{},null),$(e,d)},$$slots:{default:!0}}))},ot=H=>{kt(H,tt(()=>s(b),{get color(){return s(k)},get variant(){return s(T)},$$events:{click:q,keyup(e){l.call(this,t,e)},keydown(e){l.call(this,t,e)},mousedown(e){l.call(this,t,e)},mouseover(e){l.call(this,t,e)},focus(e){l.call(this,t,e)},mouseleave(e){l.call(this,t,e)},blur(e){l.call(this,t,e)},contextmenu(e){l.call(this,t,e)}},children:(e,W)=>{var d=P(),C=M(d);R(C,t,"default",{},null),$(e,d)},$$slots:{default:!0,iconLeft:(e,W)=>{var d=P(),C=M(d);R(C,t,"iconLeft",{},null),$(e,d)},iconRight:(e,W)=>{var d=P(),C=M(d);R(C,t,"iconRight",{},null),$(e,d)}}}))};X(U,H=>{u()?H(ct):H(ot,!1)}),$(o,f)},$$slots:{default:!0},$$legacy:!0}),$(K,v),nt()}var Ot=I('<label class="c-text-area-label svelte-c1sr7w"> </label>'),_t=I('<div class="c-text-area-label-container svelte-c1sr7w"><!> <!></div>'),Et=I("<textarea></textarea>"),Ht=I('<div class="c-text-area svelte-c1sr7w"><!> <!></div>');function St(K,t){const Q=ht(t),A=G(t,["children","$$slots","$$events","$$legacy"]),y=G(A,["label","variant","size","color","resize","textInput","type","value","id"]);at(t,!1);const b=g(),T=g(),w=g();let x=i(t,"label",24,()=>{}),B=i(t,"variant",8,"surface"),S=i(t,"size",8,2),Z=i(t,"color",24,()=>{}),_=i(t,"resize",8,"none"),u=i(t,"textInput",28,()=>{}),V=i(t,"type",8,"default"),j=i(t,"value",12,""),Y=i(t,"id",24,()=>{});function r(){if(!u())return;u(u().style.height="auto",!0);const v=.8*window.innerHeight,L=Math.min(u().scrollHeight,v);u(u().style.height=`${L}px`,!0),u(u().style.overflowY=u().scrollHeight>v?"auto":"hidden",!0)}dt(()=>{if(u()){r();const v=()=>r();return window.addEventListener("resize",v),()=>{window.removeEventListener("resize",v)}}}),N(()=>O(Y()),()=>{c(b,Y()||`text-field-${Math.random().toString(36).substring(2,11)}`)}),N(()=>(s(T),s(w),O(y)),()=>{c(T,y.class),c(w,rt(y,["class"]))}),st(),lt();var k=Ht(),E=F(k),D=v=>{var L=_t(),n=F(L),a=h=>{var f=Ot(),U=F(f);$t(()=>{mt(f,"for",s(b)),yt(U,x())}),$(h,f)};X(n,h=>{x()&&h(a)});var o=J(n,2);R(o,t,"topRightAction",{},null),$(v,L)};X(E,v=>{O(x()),it(()=>x()||Q.topRightAction)&&v(D)});var q=J(E,2);Ct(q,{get type(){return V()},get variant(){return B()},get size(){return S()},get color(){return Z()},children:(v,L)=>{var n=Et();ft(n,a=>({id:s(b),spellCheck:"false",class:`c-text-area__input c-base-text-input__input ${s(T)}`,...s(w),[pt]:a}),[()=>({"c-textarea--resize-none":_()==="none","c-textarea--resize-both":_()==="both","c-textarea--resize-horizontal":_()==="horizontal","c-textarea--resize-vertical":_()==="vertical"})],"svelte-c1sr7w"),gt(n,a=>u(a),()=>u()),p(()=>Tt(n,j)),wt(n,a=>function(o){r();const h=()=>r();return o.addEventListener("input",h),setTimeout(r,0),{destroy(){o.removeEventListener("input",h)}}}(a)),p(()=>m("click",n,function(a){l.call(this,t,a)})),p(()=>m("focus",n,function(a){l.call(this,t,a)})),p(()=>m("keydown",n,function(a){l.call(this,t,a)})),p(()=>m("change",n,function(a){l.call(this,t,a)})),p(()=>m("input",n,function(a){l.call(this,t,a)})),p(()=>m("keyup",n,function(a){l.call(this,t,a)})),p(()=>m("blur",n,function(a){l.call(this,t,a)})),p(()=>m("select",n,function(a){l.call(this,t,a)})),p(()=>m("mouseup",n,function(a){l.call(this,t,a)})),$(v,n)},$$slots:{default:!0}}),$(K,k),nt()}export{Nt as S,St as T};
