import{A as w,B as t,C as o,I as d,K as k,Y as A,a5 as D,a6 as G,H as r,m as n,a7 as I,Z as L,t as j,b as S,S as x,D as e,G as c}from"./SpinnerAugment-kH3m-zOb.js";var y=k("<span> </span>");function C(f,a){w(a,!1);let g=t(a,"class",8,""),p=t(a,"iconName",8,""),l=t(a,"fill",8,!1),m=t(a,"grade",8,"normal"),v=t(a,"title",24,()=>{}),h=n(),b=n(),i=n();o(()=>c(l()),()=>{e(h,l()?"1":"0")}),o(()=>c(l()),()=>{e(b,l()?"700":"400")}),o(()=>c(m()),()=>{switch(m()){case"low":e(i,"-25");break;case"normal":e(i,"0");break;case"high":e(i,"200")}}),d();var s=y(),$=j(s);A(()=>{D(s,1,`material-symbols-outlined ${g()}`,"svelte-htlsjs"),G(s,`font-variation-settings: 'FILL' ${r(h)??""}, 'wght' ${r(b)??""}, 'GRAD' ${r(i)??""};`),I(s,"title",v()),L($,p())}),S(f,s),x()}export{C as M};
