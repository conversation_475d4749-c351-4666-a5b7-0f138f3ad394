var U=Object.defineProperty;var ee=(r,e,t)=>e in r?U(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t;var w=(r,e,t)=>ee(r,typeof e!="symbol"?e+"":e,t);import{l as b,A as te,B as a,ai as se,J as ae,K as re,N as oe,R as _,t as n,Q as i,a4 as le,O as C,H as F,m as E,b as S,S as ie,D as f,T as A,X as L,Y as ce,Z as ne,w as O}from"./SpinnerAugment-kH3m-zOb.js";import{W as de}from"./IconButtonAugment-C8Qb_O9b.js";import"./BaseTextInput-TeF8u93x.js";import{T as ue}from"./TextAreaAugment-IsRYkfgU.js";import{l as pe}from"./chevron-down-BbSBSK7f.js";import{R as o,a as he}from"./index-BnlWKkvq.js";var me=re('<div class="l-markdown-editor svelte-1dcrmc3"><div class="c-markdown-editor__header svelte-1dcrmc3"><!></div> <!> <div class="c-markdown-editor__content svelte-1dcrmc3"><!> <!></div></div> <div class="c-markdown-editor__status svelte-1dcrmc3"><!> <!></div>',1);function Se(r,e){const t=b(e,["children","$$slots","$$events","$$legacy"]),d=b(t,["variant","size","color","resize","textInput","value","selectedText","selectionStart","selectionEnd","saveFunction","debounceValue"]);te(e,!1);let T,K=a(e,"variant",8,"surface"),M=a(e,"size",8,2),P=a(e,"color",24,()=>{}),W=a(e,"resize",8,"none"),l=a(e,"textInput",28,()=>{}),u=a(e,"value",12,""),$=a(e,"selectedText",12,""),p=a(e,"selectionStart",12,0),h=a(e,"selectionEnd",12,0),D=a(e,"saveFunction",8),N=a(e,"debounceValue",8,2500),m=E(!1),g=E();const y=async()=>{try{D()(),f(m,!0),clearTimeout(T),T=setTimeout(()=>{f(m,!1)},1500)}catch(s){f(g,s instanceof Error?s.message:String(s))}};function v(){l()&&(p(l().selectionStart),h(l().selectionEnd),p()!==h()?$(u().substring(p(),h())):$(""))}const V=pe.debounce(y,N());se(()=>{y()}),ae();var q=me(),B=oe(q),I=n(B),G=n(I);_(G,e,"header",{},null);var x=i(I,2);_(x,e,"default",{},null);var H=i(x,2),k=n(H);_(k,e,"title",{},null);var J=i(k,2);ue(J,le({get variant(){return K()},get size(){return M()},get color(){return P()},get resize(){return W()},placeholder:"Enter markdown content..."},()=>d,{get textInput(){return l()},set textInput(s){l(s)},get value(){return u()},set value(s){u(s)},$$events:{select:v,mouseup:v,keyup:()=>{v()},input:V,keydown:s=>{(s.key==="Escape"||(s.metaKey||s.ctrlKey)&&s.key==="s")&&(s.preventDefault(),y())}},$$legacy:!0}));var Q=i(B,2),z=n(Q),X=s=>{A(s,{size:1,weight:"light",color:"error",children:(R,j)=>{var c=L();ce(()=>ne(c,F(g))),S(R,c)},$$slots:{default:!0}})};C(z,s=>{F(g)&&s(X)});var Y=i(z,2),Z=s=>{A(s,{size:1,weight:"light",color:"success",children:(R,j)=>{var c=L("Saved");S(R,c)},$$slots:{default:!0}})};C(Y,s=>{F(m)&&s(Z)}),S(r,q),ie()}class fe{constructor(e){w(this,"_rulesFiles",O([]));w(this,"_loading",O(!0));this._msgBroker=e,this.requestRules()}handleMessageFromExtension(e){return!(!e.data||e.data.type!==de.getRulesListResponse)&&(this._rulesFiles.set(e.data.data),this._loading.set(!1),!0)}async requestRules(){this._loading.set(!0);try{const e=await this._msgBroker.sendToSidecar({type:o.getRulesListRequest,data:{includeGuidelines:!0}});this._rulesFiles.set(e.data.rules)}catch(e){console.error("Failed to get rules list:",e)}finally{this._loading.set(!1)}}async createRule(e){try{const t=await this._msgBroker.sendToSidecar({type:o.createRule,data:{ruleName:e.trim()}});return await this.requestRules(),t.data.createdRule||null}catch(t){throw console.error("Failed to create rule:",t),t}}async getWorkspaceRoot(){try{return(await this._msgBroker.sendToSidecar({type:o.getWorkspaceRoot})).data.workspaceRoot||""}catch(e){return console.error("Failed to get workspace root:",e),""}}async updateRuleContent(e){const t=he.formatRuleFileForMarkdown(e);try{await this._msgBroker.sendToSidecar({type:o.updateRuleFile,data:{path:e.path,content:t}})}catch(d){console.error("Failed to update rule file:",d)}await this.requestRules()}async deleteRule(e){try{await this._msgBroker.sendToSidecar({type:o.deleteRule,data:{path:e,confirmed:!0}}),await this.requestRules()}catch(t){throw console.error("Failed to delete rule:",t),t}}async processSelectedPaths(e){try{const t=await this._msgBroker.sendToSidecar({type:o.processSelectedPathsRequest,data:{selectedPaths:e,autoImport:!0}});return await this.requestRules(),{importedRulesCount:t.data.importedRulesCount,directoryOrFile:t.data.directoryOrFile,errors:t.data.errors}}catch(t){throw console.error("Failed to process selected paths:",t),t}}async getAutoImportOptions(){return await this._msgBroker.sendToSidecar({type:o.autoImportRules})}async processAutoImportSelection(e){try{const t=await this._msgBroker.sendToSidecar({type:o.autoImportRulesSelectionRequest,data:{selectedLabel:e.label}});return await this.requestRules(),{importedRulesCount:t.data.importedRulesCount,duplicatesCount:t.data.duplicatesCount,totalAttempted:t.data.totalAttempted,source:t.data.source}}catch(t){throw console.error("Failed to process auto-import selection:",t),t}}getRulesFiles(){return this._rulesFiles}getLoading(){return this._loading}}export{Se as M,fe as R};
