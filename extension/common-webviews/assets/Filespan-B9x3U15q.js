import{c as M,E as U,n as W,p as X,r as aa,ac as sa,ad as na,ae as ea,af as la,ag as ta,ah as D,z as ia,A as ca,B as f,C as d,I as ra,J as oa,T as pa,V as F,N as C,b as u,S as va,a as fa,ab as ua,K as w,O as $,P as H,Q as k,Y as J,Z as K,H as r,m as h,t as m,R as O,_ as da,a5 as ha,D as g,G as P}from"./SpinnerAugment-kH3m-zOb.js";import{n as ma,g as ga,a as _a}from"./file-paths-Bl_IgYJd.js";function ba(_,s,b,i,I,x){var o,p,l,t=null,E=_;M(()=>{const e=s()||null;var c=e==="svg"?na:null;e!==o&&(l&&(e===null?X(l,()=>{l=null,p=null}):e===p?aa(l):(sa(l),D(!1))),e&&e!==p&&(l=W(()=>{if(t=c?document.createElementNS(c,e):document.createElement(e),ea(t,t),i){var z=t.appendChild(la());i(t,z)}ta.nodes_end=t,E.before(t)})),(o=e)&&(p=o),D(!0))},U)}var Ia=w('<div><div class="c-filespan__dir-text svelte-9pfhnp"> </div></div>'),xa=w('<span class="right-icons svelte-9pfhnp"><!></span>'),Ea=w('<!> <span class="c-filespan__filename svelte-9pfhnp"> </span> <!> <!>',1);function ka(_,s){const b=ia(s);ca(s,!1);const i=h(),I=h(),x=h(),o=h();let p=f(s,"class",8,""),l=f(s,"filepath",8),t=f(s,"size",8,1),E=f(s,"nopath",8,!1),e=f(s,"growname",8,!0),c=f(s,"onClick",24,()=>{});d(()=>P(l()),()=>{g(i,ma(l()))}),d(()=>r(i),()=>{g(I,ga(r(i)))}),d(()=>r(i),()=>{g(x,_a(r(i)))}),d(()=>P(c()),()=>{g(o,c()?"button":"div")}),ra(),oa(),pa(_,{get size(){return t()},children:(z,za)=>{var N=F();ba(C(N),()=>r(o),0,(S,Q)=>{fa(S,()=>({class:`c-filespan ${p()}`,role:c()?"button":"",tabindex:"0"}),void 0,"svelte-9pfhnp"),ua("click",S,function(...a){var n;(n=c())==null||n.apply(this,a)});var T=Ea(),y=C(T),R=a=>{var n=F(),v=C(n);O(v,s,"leftIcon",{},null),u(a,n)};$(y,a=>{H(()=>b.leftIcon)&&a(R)});var A=k(y,2),Z=m(A),B=k(A,2),j=a=>{var n=Ia();let v;var Y=m(n),q=m(Y);J(L=>{v=ha(n,1,"c-filespan__dir svelte-9pfhnp",null,v,L),K(q,r(x))},[()=>({growname:e()})],da),u(a,n)};$(B,a=>{E()||a(j)});var G=k(B,2),V=a=>{var n=xa(),v=m(n);O(v,s,"rightIcon",{},null),u(a,n)};$(G,a=>{H(()=>b.rightIcon)&&a(V)}),J(()=>K(Z,r(I))),u(Q,T)}),u(z,N)},$$slots:{default:!0}}),va()}export{ka as F,ba as e};
