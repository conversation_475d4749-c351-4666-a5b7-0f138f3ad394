var ni=Object.defineProperty;var ri=(t,e,n)=>e in t?ni(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var G=(t,e,n)=>ri(t,typeof e!="symbol"?e+"":e,n);import{f as St,b as he,l as lt,A as Bs,u as si,B as de,C as zs,D as On,m as Rn,F as oi,G as Dn,H as $e,I as Ws,J as ii,K as sr,a4 as ai,V as ci,N as Js,R as ui,_ as Ln,t as Re,Y as Nn,a5 as it,P as di,S as Gs,O as li,Q as Sr,a7 as pi,ab as wr,Z as Er,w as fi,aD as dn,a as or}from"./SpinnerAugment-kH3m-zOb.js";import{B as hi,k as mi}from"./user-DigCn7eq.js";import{C as gi,b as ie,h as ir}from"./IconButtonAugment-C8Qb_O9b.js";import{a as _i}from"./BaseTextInput-TeF8u93x.js";import{a3 as ln}from"./index-BnlWKkvq.js";const C=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,De="9.20.0",N=globalThis;function ze(){return en(N),N}function en(t){const e=t.__SENTRY__=t.__SENTRY__||{};return e.version=e.version||De,e[De]=e[De]||{}}function Wt(t,e,n=N){const r=n.__SENTRY__=n.__SENTRY__||{},s=r[De]=r[De]||{};return s[t]||(s[t]=e())}const Vs=Object.prototype.toString;function ar(t){switch(Vs.call(t)){case"[object Error]":case"[object Exception]":case"[object DOMException]":case"[object WebAssembly.Exception]":return!0;default:return me(t,Error)}}function nt(t,e){return Vs.call(t)===`[object ${e}]`}function Ys(t){return nt(t,"ErrorEvent")}function xr(t){return nt(t,"DOMError")}function fe(t){return nt(t,"String")}function cr(t){return typeof t=="object"&&t!==null&&"__sentry_template_string__"in t&&"__sentry_template_values__"in t}function pt(t){return t===null||cr(t)||typeof t!="object"&&typeof t!="function"}function ft(t){return nt(t,"Object")}function tn(t){return typeof Event<"u"&&me(t,Event)}function nn(t){return!!(t!=null&&t.then&&typeof t.then=="function")}function me(t,e){try{return t instanceof e}catch{return!1}}function Ks(t){return!(typeof t!="object"||t===null||!t.__isVue&&!t._isVue)}function Xs(t){return typeof Request<"u"&&me(t,Request)}const ur=N,vi=80;function qe(t,e={}){if(!t)return"<unknown>";try{let n=t;const r=5,s=[];let o=0,i=0;const a=" > ",c=a.length;let u;const p=Array.isArray(e)?e:e.keyAttrs,h=!Array.isArray(e)&&e.maxStringLength||vi;for(;n&&o++<r&&(u=yi(n,p),!(u==="html"||o>1&&i+s.length*c+u.length>=h));)s.push(u),i+=u.length,n=n.parentNode;return s.reverse().join(a)}catch{return"<unknown>"}}function yi(t,e){const n=t,r=[];if(!(n!=null&&n.tagName))return"";if(ur.HTMLElement&&n instanceof HTMLElement&&n.dataset){if(n.dataset.sentryComponent)return n.dataset.sentryComponent;if(n.dataset.sentryElement)return n.dataset.sentryElement}r.push(n.tagName.toLowerCase());const s=e!=null&&e.length?e.filter(i=>n.getAttribute(i)).map(i=>[i,n.getAttribute(i)]):null;if(s!=null&&s.length)s.forEach(i=>{r.push(`[${i[0]}="${i[1]}"]`)});else{n.id&&r.push(`#${n.id}`);const i=n.className;if(i&&fe(i)){const a=i.split(/\s+/);for(const c of a)r.push(`.${c}`)}}const o=["aria-label","type","name","title","alt"];for(const i of o){const a=n.getAttribute(i);a&&r.push(`[${i}="${a}"]`)}return r.join("")}function wt(){try{return ur.document.location.href}catch{return""}}function Zs(t){if(!ur.HTMLElement)return null;let e=t;for(let n=0;n<5;n++){if(!e)return null;if(e instanceof HTMLElement){if(e.dataset.sentryComponent)return e.dataset.sentryComponent;if(e.dataset.sentryElement)return e.dataset.sentryElement}e=e.parentNode}return null}const Mn=["debug","info","warn","error","log","assert","trace"],Jt={};function We(t){if(!("console"in N))return t();const e=N.console,n={},r=Object.keys(Jt);r.forEach(s=>{const o=Jt[s];n[s]=e[s],e[s]=o});try{return t()}finally{r.forEach(s=>{e[s]=n[s]})}}const w=Wt("logger",function(){let t=!1;const e={enable:()=>{t=!0},disable:()=>{t=!1},isEnabled:()=>t};return C?Mn.forEach(n=>{e[n]=(...r)=>{t&&We(()=>{N.console[n](`Sentry Logger [${n}]:`,...r)})}}):Mn.forEach(n=>{e[n]=()=>{}}),e});function Gt(t,e=0){return typeof t!="string"||e===0||t.length<=e?t:`${t.slice(0,e)}...`}function Tr(t,e){if(!Array.isArray(t))return"";const n=[];for(let r=0;r<t.length;r++){const s=t[r];try{Ks(s)?n.push("[VueViewModel]"):n.push(String(s))}catch{n.push("[value cannot be serialized]")}}return n.join(e)}function bi(t,e,n=!1){return!!fe(t)&&(nt(e,"RegExp")?e.test(t):!!fe(e)&&(n?t===e:t.includes(e)))}function Oe(t,e=[],n=!1){return e.some(r=>bi(t,r,n))}function Z(t,e,n){if(!(e in t))return;const r=t[e];if(typeof r!="function")return;const s=n(r);typeof s=="function"&&Qs(s,r);try{t[e]=s}catch{C&&w.log(`Failed to replace method "${e}" in object`,t)}}function Q(t,e,n){try{Object.defineProperty(t,e,{value:n,writable:!0,configurable:!0})}catch{C&&w.log(`Failed to add non-enumerable property "${e}" to object`,t)}}function Qs(t,e){try{const n=e.prototype||{};t.prototype=e.prototype=n,Q(t,"__sentry_original__",e)}catch{}}function dr(t){return t.__sentry_original__}function eo(t){if(ar(t))return{message:t.message,name:t.name,stack:t.stack,...Cr(t)};if(tn(t)){const e={type:t.type,target:kr(t.target),currentTarget:kr(t.currentTarget),...Cr(t)};return typeof CustomEvent<"u"&&me(t,CustomEvent)&&(e.detail=t.detail),e}return t}function kr(t){try{return e=t,typeof Element<"u"&&me(e,Element)?qe(t):Object.prototype.toString.call(t)}catch{return"<unknown>"}var e}function Cr(t){if(typeof t=="object"&&t!==null){const e={};for(const n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}return{}}function ne(t=function(){const e=N;return e.crypto||e.msCrypto}()){let e=()=>16*Math.random();try{if(t!=null&&t.randomUUID)return t.randomUUID().replace(/-/g,"");t!=null&&t.getRandomValues&&(e=()=>{const n=new Uint8Array(1);return t.getRandomValues(n),n[0]})}catch{}return("10000000100040008000"+1e11).replace(/[018]/g,n=>(n^(15&e())>>n/4).toString(16))}function to(t){var e,n;return(n=(e=t.exception)==null?void 0:e.values)==null?void 0:n[0]}function Pe(t){const{message:e,event_id:n}=t;if(e)return e;const r=to(t);return r?r.type&&r.value?`${r.type}: ${r.value}`:r.type||r.value||n||"<unknown>":n||"<unknown>"}function jn(t,e,n){const r=t.exception=t.exception||{},s=r.values=r.values||[],o=s[0]=s[0]||{};o.value||(o.value=e||""),o.type||(o.type="Error")}function Ye(t,e){const n=to(t);if(!n)return;const r=n.mechanism;if(n.mechanism={type:"generic",handled:!0,...r,...e},e&&"data"in e){const s={...r==null?void 0:r.data,...e.data};n.mechanism.data=s}}function Ir(t){if(function(e){try{return e.__sentry_captured__}catch{}}(t))return!0;try{Q(t,"__sentry_captured__",!0)}catch{}return!1}const no=1e3;function Et(){return Date.now()/no}const Y=function(){const{performance:t}=N;if(!(t!=null&&t.now))return Et;const e=Date.now()-t.now(),n=t.timeOrigin==null?e:t.timeOrigin;return()=>(n+t.now())/no}();let pn;function te(){return pn||(pn=function(){var c;const{performance:t}=N;if(!(t!=null&&t.now))return[void 0,"none"];const e=36e5,n=t.now(),r=Date.now(),s=t.timeOrigin?Math.abs(t.timeOrigin+n-r):e,o=s<e,i=(c=t.timing)==null?void 0:c.navigationStart,a=typeof i=="number"?Math.abs(i+n-r):e;return o||a<e?s<=a?[t.timeOrigin,"timeOrigin"]:[i,"navigationStart"]:[r,"dateNow"]}()),pn[0]}function Si(t){const e=Y(),n={sid:ne(),init:!0,timestamp:e,started:e,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>function(r){return{sid:`${r.sid}`,init:r.init,started:new Date(1e3*r.started).toISOString(),timestamp:new Date(1e3*r.timestamp).toISOString(),status:r.status,errors:r.errors,did:typeof r.did=="number"||typeof r.did=="string"?`${r.did}`:void 0,duration:r.duration,abnormal_mechanism:r.abnormal_mechanism,attrs:{release:r.release,environment:r.environment,ip_address:r.ipAddress,user_agent:r.userAgent}}}(n)};return t&&Ke(n,t),n}function Ke(t,e={}){if(e.user&&(!t.ipAddress&&e.user.ip_address&&(t.ipAddress=e.user.ip_address),t.did||e.did||(t.did=e.user.id||e.user.email||e.user.username)),t.timestamp=e.timestamp||Y(),e.abnormal_mechanism&&(t.abnormal_mechanism=e.abnormal_mechanism),e.ignoreDuration&&(t.ignoreDuration=e.ignoreDuration),e.sid&&(t.sid=e.sid.length===32?e.sid:ne()),e.init!==void 0&&(t.init=e.init),!t.did&&e.did&&(t.did=`${e.did}`),typeof e.started=="number"&&(t.started=e.started),t.ignoreDuration)t.duration=void 0;else if(typeof e.duration=="number")t.duration=e.duration;else{const n=t.timestamp-t.started;t.duration=n>=0?n:0}e.release&&(t.release=e.release),e.environment&&(t.environment=e.environment),!t.ipAddress&&e.ipAddress&&(t.ipAddress=e.ipAddress),!t.userAgent&&e.userAgent&&(t.userAgent=e.userAgent),typeof e.errors=="number"&&(t.errors=e.errors),e.status&&(t.status=e.status)}function xt(t,e,n=2){if(!e||typeof e!="object"||n<=0)return e;if(t&&Object.keys(e).length===0)return t;const r={...t};for(const s in e)Object.prototype.hasOwnProperty.call(e,s)&&(r[s]=xt(r[s],e[s],n-1));return r}const Fn="_sentrySpan";function ht(t,e){e?Q(t,Fn,e):delete t[Fn]}function Vt(t){return t[Fn]}function be(){return ne()}function Tt(){return ne().substring(16)}class ge{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext={traceId:be(),sampleRand:Math.random()}}clone(){const e=new ge;return e._breadcrumbs=[...this._breadcrumbs],e._tags={...this._tags},e._extra={...this._extra},e._contexts={...this._contexts},this._contexts.flags&&(e._contexts.flags={values:[...this._contexts.flags.values]}),e._user=this._user,e._level=this._level,e._session=this._session,e._transactionName=this._transactionName,e._fingerprint=this._fingerprint,e._eventProcessors=[...this._eventProcessors],e._attachments=[...this._attachments],e._sdkProcessingMetadata={...this._sdkProcessingMetadata},e._propagationContext={...this._propagationContext},e._client=this._client,e._lastEventId=this._lastEventId,ht(e,Vt(this)),e}setClient(e){this._client=e}setLastEventId(e){this._lastEventId=e}getClient(){return this._client}lastEventId(){return this._lastEventId}addScopeListener(e){this._scopeListeners.push(e)}addEventProcessor(e){return this._eventProcessors.push(e),this}setUser(e){return this._user=e||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this._session&&Ke(this._session,{user:e}),this._notifyScopeListeners(),this}getUser(){return this._user}setTags(e){return this._tags={...this._tags,...e},this._notifyScopeListeners(),this}setTag(e,n){return this._tags={...this._tags,[e]:n},this._notifyScopeListeners(),this}setExtras(e){return this._extra={...this._extra,...e},this._notifyScopeListeners(),this}setExtra(e,n){return this._extra={...this._extra,[e]:n},this._notifyScopeListeners(),this}setFingerprint(e){return this._fingerprint=e,this._notifyScopeListeners(),this}setLevel(e){return this._level=e,this._notifyScopeListeners(),this}setTransactionName(e){return this._transactionName=e,this._notifyScopeListeners(),this}setContext(e,n){return n===null?delete this._contexts[e]:this._contexts[e]=n,this._notifyScopeListeners(),this}setSession(e){return e?this._session=e:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(e){if(!e)return this;const n=typeof e=="function"?e(this):e,r=n instanceof ge?n.getScopeData():ft(n)?e:void 0,{tags:s,extra:o,user:i,contexts:a,level:c,fingerprint:u=[],propagationContext:p}=r||{};return this._tags={...this._tags,...s},this._extra={...this._extra,...o},this._contexts={...this._contexts,...a},i&&Object.keys(i).length&&(this._user=i),c&&(this._level=c),u.length&&(this._fingerprint=u),p&&(this._propagationContext=p),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._session=void 0,ht(this,void 0),this._attachments=[],this.setPropagationContext({traceId:be(),sampleRand:Math.random()}),this._notifyScopeListeners(),this}addBreadcrumb(e,n){var o;const r=typeof n=="number"?n:100;if(r<=0)return this;const s={timestamp:Et(),...e,message:e.message?Gt(e.message,2048):e.message};return this._breadcrumbs.push(s),this._breadcrumbs.length>r&&(this._breadcrumbs=this._breadcrumbs.slice(-r),(o=this._client)==null||o.recordDroppedEvent("buffer_overflow","log_item")),this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(e){return this._attachments.push(e),this}clearAttachments(){return this._attachments=[],this}getScopeData(){return{breadcrumbs:this._breadcrumbs,attachments:this._attachments,contexts:this._contexts,tags:this._tags,extra:this._extra,user:this._user,level:this._level,fingerprint:this._fingerprint||[],eventProcessors:this._eventProcessors,propagationContext:this._propagationContext,sdkProcessingMetadata:this._sdkProcessingMetadata,transactionName:this._transactionName,span:Vt(this)}}setSDKProcessingMetadata(e){return this._sdkProcessingMetadata=xt(this._sdkProcessingMetadata,e,2),this}setPropagationContext(e){return this._propagationContext=e,this}getPropagationContext(){return this._propagationContext}captureException(e,n){const r=(n==null?void 0:n.event_id)||ne();if(!this._client)return w.warn("No client configured on scope - will not capture exception!"),r;const s=new Error("Sentry syntheticException");return this._client.captureException(e,{originalException:e,syntheticException:s,...n,event_id:r},this),r}captureMessage(e,n,r){const s=(r==null?void 0:r.event_id)||ne();if(!this._client)return w.warn("No client configured on scope - will not capture message!"),s;const o=new Error(e);return this._client.captureMessage(e,n,{originalException:e,syntheticException:o,...r,event_id:s},this),s}captureEvent(e,n){const r=(n==null?void 0:n.event_id)||ne();return this._client?(this._client.captureEvent(e,{...n,event_id:r},this),r):(w.warn("No client configured on scope - will not capture event!"),r)}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach(e=>{e(this)}),this._notifyingListeners=!1)}}class wi{constructor(e,n){let r,s;r=e||new ge,s=n||new ge,this._stack=[{scope:r}],this._isolationScope=s}withScope(e){const n=this._pushScope();let r;try{r=e(n)}catch(s){throw this._popScope(),s}return nn(r)?r.then(s=>(this._popScope(),s),s=>{throw this._popScope(),s}):(this._popScope(),r)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStackTop(){return this._stack[this._stack.length-1]}_pushScope(){const e=this.getScope().clone();return this._stack.push({client:this.getClient(),scope:e}),e}_popScope(){return!(this._stack.length<=1)&&!!this._stack.pop()}}function Xe(){const t=en(ze());return t.stack=t.stack||new wi(Wt("defaultCurrentScope",()=>new ge),Wt("defaultIsolationScope",()=>new ge))}function Ei(t){return Xe().withScope(t)}function xi(t,e){const n=Xe();return n.withScope(()=>(n.getStackTop().scope=t,e(t)))}function $r(t){return Xe().withScope(()=>t(Xe().getIsolationScope()))}function rt(t){const e=en(t);return e.acs?e.acs:{withIsolationScope:$r,withScope:Ei,withSetScope:xi,withSetIsolationScope:(n,r)=>$r(r),getCurrentScope:()=>Xe().getScope(),getIsolationScope:()=>Xe().getIsolationScope()}}function q(){return rt(ze()).getCurrentScope()}function ke(){return rt(ze()).getIsolationScope()}function mt(...t){const e=rt(ze());if(t.length===2){const[n,r]=t;return n?e.withSetScope(n,r):e.withScope(r)}return e.withScope(t[0])}function j(){return q().getClient()}function Ti(t){const e=t.getPropagationContext(),{traceId:n,parentSpanId:r,propagationSpanId:s}=e,o={trace_id:n,span_id:s||Tt()};return r&&(o.parent_span_id=r),o}const le="sentry.source",lr="sentry.sample_rate",ro="sentry.previous_trace_sample_rate",Ue="sentry.op",B="sentry.origin",Yt="sentry.idle_span_finish_reason",rn="sentry.measurement_unit",sn="sentry.measurement_value",Pr="sentry.custom_span_name",qn="sentry.profile_id",gt="sentry.exclusive_time",ki="sentry.link.type",Ci=0,so=1,H=2;function oo(t,e){t.setAttribute("http.response.status_code",e);const n=function(r){if(r<400&&r>=100)return{code:so};if(r>=400&&r<500)switch(r){case 401:return{code:H,message:"unauthenticated"};case 403:return{code:H,message:"permission_denied"};case 404:return{code:H,message:"not_found"};case 409:return{code:H,message:"already_exists"};case 413:return{code:H,message:"failed_precondition"};case 429:return{code:H,message:"resource_exhausted"};case 499:return{code:H,message:"cancelled"};default:return{code:H,message:"invalid_argument"}}if(r>=500&&r<600)switch(r){case 501:return{code:H,message:"unimplemented"};case 503:return{code:H,message:"unavailable"};case 504:return{code:H,message:"deadline_exceeded"};default:return{code:H,message:"internal_error"}}return{code:H,message:"unknown_error"}}(e);n.message!=="unknown_error"&&t.setStatus(n)}const io="_sentryScope",ao="_sentryIsolationScope";function Kt(t){return{scope:t[io],isolationScope:t[ao]}}function _t(t){if(typeof t=="boolean")return Number(t);const e=typeof t=="string"?parseFloat(t):t;return typeof e!="number"||isNaN(e)||e<0||e>1?void 0:e}const pr="sentry-",Ii=/^sentry-/,$i=8192;function co(t){const e=function(r){if(!(!r||!fe(r)&&!Array.isArray(r)))return Array.isArray(r)?r.reduce((s,o)=>{const i=Ar(o);return Object.entries(i).forEach(([a,c])=>{s[a]=c}),s},{}):Ar(r)}(t);if(!e)return;const n=Object.entries(e).reduce((r,[s,o])=>(s.match(Ii)&&(r[s.slice(pr.length)]=o),r),{});return Object.keys(n).length>0?n:void 0}function Pi(t){if(t)return function(e){if(Object.keys(e).length!==0)return Object.entries(e).reduce((n,[r,s],o)=>{const i=`${encodeURIComponent(r)}=${encodeURIComponent(s)}`,a=o===0?i:`${n},${i}`;return a.length>$i?(C&&w.warn(`Not adding key: ${r} with val: ${s} to baggage header due to exceeding baggage size limits.`),n):a},"")}(Object.entries(t).reduce((e,[n,r])=>(r&&(e[`${pr}${n}`]=r),e),{}))}function Ar(t){return t.split(",").map(e=>e.split("=").map(n=>{try{return decodeURIComponent(n.trim())}catch{return}})).reduce((e,[n,r])=>(n&&r&&(e[n]=r),e),{})}const uo=new RegExp("^[ \\t]*([0-9a-f]{32})?-?([0-9a-f]{16})?-?([01])?[ \\t]*$");function Ai(t,e){const n=function(c){if(!c)return;const u=c.match(uo);if(!u)return;let p;return u[3]==="1"?p=!0:u[3]==="0"&&(p=!1),{traceId:u[1],parentSampled:p,parentSpanId:u[2]}}(t),r=co(e);if(!(n!=null&&n.traceId))return{traceId:be(),sampleRand:Math.random()};const s=function(c,u){const p=_t(u==null?void 0:u.sample_rand);if(p!==void 0)return p;const h=_t(u==null?void 0:u.sample_rate);return h&&(c==null?void 0:c.parentSampled)!==void 0?c.parentSampled?Math.random()*h:h+Math.random()*(1-h):Math.random()}(n,r);r&&(r.sample_rand=s.toString());const{traceId:o,parentSpanId:i,parentSampled:a}=n;return{traceId:o,parentSpanId:i,sampled:a,dsc:r||{},sampleRand:s}}function Or(t=be(),e=Tt(),n){let r="";return n!==void 0&&(r=n?"-1":"-0"),`${t}-${e}${r}`}const fr=1;let Rr=!1;function Oi(t){const{spanId:e,traceId:n}=t.spanContext(),{data:r,op:s,parent_span_id:o,status:i,origin:a,links:c}=F(t);return{parent_span_id:o,span_id:e,trace_id:n,data:r,op:s,status:i,origin:a,links:c}}function Ri(t){const{spanId:e,traceId:n,isRemote:r}=t.spanContext(),s=r?e:F(t).parent_span_id,o=Kt(t).scope;return{parent_span_id:s,span_id:r?(o==null?void 0:o.getPropagationContext().propagationSpanId)||Tt():e,trace_id:n}}function lo(t){return t&&t.length>0?t.map(({context:{spanId:e,traceId:n,traceFlags:r,...s},attributes:o})=>({span_id:e,trace_id:n,sampled:r===fr,attributes:o,...s})):void 0}function Le(t){return typeof t=="number"?Dr(t):Array.isArray(t)?t[0]+t[1]/1e9:t instanceof Date?Dr(t.getTime()):Y()}function Dr(t){return t>9999999999?t/1e3:t}function F(t){var r;if(function(s){return typeof s.getSpanJSON=="function"}(t))return t.getSpanJSON();const{spanId:e,traceId:n}=t.spanContext();if(function(s){const o=s;return!!(o.attributes&&o.startTime&&o.name&&o.endTime&&o.status)}(t)){const{attributes:s,startTime:o,name:i,endTime:a,status:c,links:u}=t;return{span_id:e,trace_id:n,data:s,description:i,parent_span_id:"parentSpanId"in t?t.parentSpanId:"parentSpanContext"in t?(r=t.parentSpanContext)==null?void 0:r.spanId:void 0,start_timestamp:Le(o),timestamp:Le(a)||void 0,status:po(c),op:s[Ue],origin:s[B],links:lo(u)}}return{span_id:e,trace_id:n,start_timestamp:0,data:{}}}function Ne(t){const{traceFlags:e}=t.spanContext();return e===fr}function po(t){if(t&&t.code!==Ci)return t.code===so?"ok":t.message||"unknown_error"}const Me="_sentryChildSpans",Un="_sentryRootSpan";function Lr(t,e){const n=t[Un]||t;Q(e,Un,n),t[Me]?t[Me].add(e):Q(t,Me,new Set([e]))}function Ft(t){const e=new Set;return function n(r){if(!e.has(r)&&Ne(r)){e.add(r);const s=r[Me]?Array.from(r[Me]):[];for(const o of s)n(o)}}(t),Array.from(e)}function V(t){return t[Un]||t}function X(){const t=rt(ze());return t.getActiveSpan?t.getActiveSpan():Vt(q())}function Hn(){Rr||(We(()=>{console.warn("[Sentry] Returning null from `beforeSendSpan` is disallowed. To drop certain spans, configure the respective integrations directly.")}),Rr=!0)}const Nr=50,je="?",Mr=/\(error: (.*)\)/,jr=/captureMessage|captureException/;function fo(...t){const e=t.sort((n,r)=>n[0]-r[0]).map(n=>n[1]);return(n,r=0,s=0)=>{const o=[],i=n.split(`
`);for(let a=r;a<i.length;a++){const c=i[a];if(c.length>1024)continue;const u=Mr.test(c)?c.replace(Mr,"$1"):c;if(!u.match(/\S*Error: /)){for(const p of e){const h=p(u);if(h){o.push(h);break}}if(o.length>=Nr+s)break}}return function(a){if(!a.length)return[];const c=Array.from(a);return/sentryWrapped/.test($t(c).function||"")&&c.pop(),c.reverse(),jr.test($t(c).function||"")&&(c.pop(),jr.test($t(c).function||"")&&c.pop()),c.slice(0,Nr).map(u=>({...u,filename:u.filename||$t(c).filename,function:u.function||je}))}(o.slice(s))}}function $t(t){return t[t.length-1]||{}}const Fr="<anonymous>";function _e(t){try{return t&&typeof t=="function"&&t.name||Fr}catch{return Fr}}function qr(t){const e=t.exception;if(e){const n=[];try{return e.values.forEach(r=>{r.stacktrace.frames&&n.push(...r.stacktrace.frames)}),n}catch{return}}}const qt={},Ur={};function Se(t,e){qt[t]=qt[t]||[],qt[t].push(e)}function we(t,e){if(!Ur[t]){Ur[t]=!0;try{e()}catch(n){C&&w.error(`Error while instrumenting ${t}`,n)}}}function re(t,e){const n=t&&qt[t];if(n)for(const r of n)try{r(e)}catch(s){C&&w.error(`Error while triggering instrumentation handler.
Type: ${t}
Name: ${_e(r)}
Error:`,s)}}let fn=null;function ho(t){const e="error";Se(e,t),we(e,Di)}function Di(){fn=N.onerror,N.onerror=function(t,e,n,r,s){return re("error",{column:r,error:s,line:n,msg:t,url:e}),!!fn&&fn.apply(this,arguments)},N.onerror.__SENTRY_INSTRUMENTED__=!0}let hn=null;function mo(t){const e="unhandledrejection";Se(e,t),we(e,Li)}function Li(){hn=N.onunhandledrejection,N.onunhandledrejection=function(t){return re("unhandledrejection",t),!hn||hn.apply(this,arguments)},N.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}let Hr=!1;function Bn(){const t=X(),e=t&&V(t);if(e){const n="internal_error";C&&w.log(`[Tracing] Root span: ${n} -> Global error occurred`),e.setStatus({code:H,message:n})}}function Ee(t){var n;if(typeof __SENTRY_TRACING__=="boolean"&&!__SENTRY_TRACING__)return!1;const e=t||((n=j())==null?void 0:n.getOptions());return!(!e||e.tracesSampleRate==null&&!e.tracesSampler)}Bn.tag="sentry_tracingErrorCallback";const hr="production",Ni=/^o(\d+)\./,Mi=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function vt(t,e=!1){const{host:n,path:r,pass:s,port:o,projectId:i,protocol:a,publicKey:c}=t;return`${a}://${c}${e&&s?`:${s}`:""}@${n}${o?`:${o}`:""}/${r&&`${r}/`}${i}`}function Br(t){return{protocol:t.protocol,publicKey:t.publicKey||"",pass:t.pass||"",host:t.host,port:t.port||"",path:t.path||"",projectId:t.projectId}}function ji(t){const e=typeof t=="string"?function(n){const r=Mi.exec(n);if(!r)return void We(()=>{console.error(`Invalid Sentry Dsn: ${n}`)});const[s,o,i="",a="",c="",u=""]=r.slice(1);let p="",h=u;const f=h.split("/");if(f.length>1&&(p=f.slice(0,-1).join("/"),h=f.pop()),h){const l=h.match(/^\d+/);l&&(h=l[0])}return Br({host:a,pass:i,path:p,projectId:h,port:c,protocol:s,publicKey:o})}(t):Br(t);if(e&&function(n){if(!C)return!0;const{port:r,projectId:s,protocol:o}=n;return!(["protocol","publicKey","host","projectId"].find(i=>!n[i]&&(w.error(`Invalid Sentry Dsn: ${i} missing`),!0))||(s.match(/^\d+$/)?function(i){return i==="http"||i==="https"}(o)?r&&isNaN(parseInt(r,10))&&(w.error(`Invalid Sentry Dsn: Invalid port ${r}`),1):(w.error(`Invalid Sentry Dsn: Invalid protocol ${o}`),1):(w.error(`Invalid Sentry Dsn: Invalid projectId ${s}`),1)))}(e))return e}const go="_frozenDsc";function Ut(t,e){Q(t,go,e)}function _o(t,e){const n=e.getOptions(),{publicKey:r,host:s}=e.getDsn()||{};let o;n.orgId?o=String(n.orgId):s&&(o=function(a){const c=a.match(Ni);return c==null?void 0:c[1]}(s));const i={environment:n.environment||hr,release:n.release,public_key:r,trace_id:t,org_id:o};return e.emit("createDsc",i),i}function vo(t,e){const n=e.getPropagationContext();return n.dsc||_o(n.traceId,t)}function xe(t){var d;const e=j();if(!e)return{};const n=V(t),r=F(n),s=r.data,o=n.spanContext().traceState,i=(o==null?void 0:o.get("sentry.sample_rate"))??s[lr]??s[ro];function a(m){return typeof i!="number"&&typeof i!="string"||(m.sample_rate=`${i}`),m}const c=n[go];if(c)return a(c);const u=o==null?void 0:o.get("sentry.dsc"),p=u&&co(u);if(p)return a(p);const h=_o(t.spanContext().traceId,e),f=s[le],l=r.description;return f!=="url"&&l&&(h.transaction=l),Ee()&&(h.sampled=String(Ne(n)),h.sample_rand=(o==null?void 0:o.get("sentry.sample_rand"))??((d=Kt(n).scope)==null?void 0:d.getPropagationContext().sampleRand.toString())),a(h),e.emit("createDsc",h,n),h}class Fe{constructor(e={}){this._traceId=e.traceId||be(),this._spanId=e.spanId||Tt()}spanContext(){return{spanId:this._spanId,traceId:this._traceId,traceFlags:0}}end(e){}setAttribute(e,n){return this}setAttributes(e){return this}setStatus(e){return this}updateName(e){return this}isRecording(){return!1}addEvent(e,n,r){return this}addLink(e){return this}addLinks(e){return this}recordException(e,n){}}function ae(t,e=100,n=1/0){try{return zn("",t,e,n)}catch(r){return{ERROR:`**non-serializable** (${r})`}}}function yo(t,e=3,n=102400){const r=ae(t,e);return s=r,function(o){return~-encodeURI(o).split(/%..|./).length}(JSON.stringify(s))>n?yo(t,e-1,n):r;var s}function zn(t,e,n=1/0,r=1/0,s=function(){const o=new WeakSet;function i(c){return!!o.has(c)||(o.add(c),!1)}function a(c){o.delete(c)}return[i,a]}()){const[o,i]=s;if(e==null||["boolean","string"].includes(typeof e)||typeof e=="number"&&Number.isFinite(e))return e;const a=function(l,d){try{if(l==="domain"&&d&&typeof d=="object"&&d._events)return"[Domain]";if(l==="domainEmitter")return"[DomainEmitter]";if(typeof global<"u"&&d===global)return"[Global]";if(typeof window<"u"&&d===window)return"[Window]";if(typeof document<"u"&&d===document)return"[Document]";if(Ks(d))return"[VueViewModel]";if(ft(m=d)&&"nativeEvent"in m&&"preventDefault"in m&&"stopPropagation"in m)return"[SyntheticEvent]";if(typeof d=="number"&&!Number.isFinite(d))return`[${d}]`;if(typeof d=="function")return`[Function: ${_e(d)}]`;if(typeof d=="symbol")return`[${String(d)}]`;if(typeof d=="bigint")return`[BigInt: ${String(d)}]`;const g=function(b){const v=Object.getPrototypeOf(b);return v!=null&&v.constructor?v.constructor.name:"null prototype"}(d);return/^HTML(\w*)Element$/.test(g)?`[HTMLElement: ${g}]`:`[object ${g}]`}catch(g){return`**non-serializable** (${g})`}var m}(t,e);if(!a.startsWith("[object "))return a;if(e.__sentry_skip_normalization__)return e;const c=typeof e.__sentry_override_normalization_depth__=="number"?e.__sentry_override_normalization_depth__:n;if(c===0)return a.replace("object ","");if(o(e))return"[Circular ~]";const u=e;if(u&&typeof u.toJSON=="function")try{return zn("",u.toJSON(),c-1,r,s)}catch{}const p=Array.isArray(e)?[]:{};let h=0;const f=eo(e);for(const l in f){if(!Object.prototype.hasOwnProperty.call(f,l))continue;if(h>=r){p[l]="[MaxProperties ~]";break}const d=f[l];p[l]=zn(l,d,c-1,r,s),h++}return i(e),p}function Ze(t,e=[]){return[t,e]}function Fi(t,e){const[n,r]=t;return[n,[...r,e]]}function zr(t,e){const n=t[1];for(const r of n)if(e(r,r[0].type))return!0;return!1}function Wn(t){const e=en(N);return e.encodePolyfill?e.encodePolyfill(t):new TextEncoder().encode(t)}function qi(t){const[e,n]=t;let r=JSON.stringify(e);function s(o){typeof r=="string"?r=typeof o=="string"?r+o:[Wn(r),o]:r.push(typeof o=="string"?Wn(o):o)}for(const o of n){const[i,a]=o;if(s(`
${JSON.stringify(i)}
`),typeof a=="string"||a instanceof Uint8Array)s(a);else{let c;try{c=JSON.stringify(a)}catch{c=JSON.stringify(ae(a))}s(c)}}return typeof r=="string"?r:function(o){const i=o.reduce((u,p)=>u+p.length,0),a=new Uint8Array(i);let c=0;for(const u of o)a.set(u,c),c+=u.length;return a}(r)}function Ui(t){return[{type:"span"},t]}function Hi(t){const e=typeof t.data=="string"?Wn(t.data):t.data;return[{type:"attachment",length:e.length,filename:t.filename,content_type:t.contentType,attachment_type:t.attachmentType},e]}const Bi={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",profile_chunk:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",raw_security:"security",log:"log_item"};function Wr(t){return Bi[t]}function bo(t){if(!(t!=null&&t.sdk))return;const{name:e,version:n}=t.sdk;return{name:e,version:n}}function zi(t,e,n,r){const s=bo(n),o=t.type&&t.type!=="replay_event"?t.type:"event";(function(a,c){c&&(a.sdk=a.sdk||{},a.sdk.name=a.sdk.name||c.name,a.sdk.version=a.sdk.version||c.version,a.sdk.integrations=[...a.sdk.integrations||[],...c.integrations||[]],a.sdk.packages=[...a.sdk.packages||[],...c.packages||[]])})(t,n==null?void 0:n.sdk);const i=function(a,c,u,p){var f;const h=(f=a.sdkProcessingMetadata)==null?void 0:f.dynamicSamplingContext;return{event_id:a.event_id,sent_at:new Date().toISOString(),...c&&{sdk:c},...!!u&&p&&{dsn:vt(p)},...h&&{trace:h}}}(t,s,r,e);return delete t.sdkProcessingMetadata,Ze(i,[[{type:o},t]])}function Jr(t){if(!t||t.length===0)return;const e={};return t.forEach(n=>{const r=n.attributes||{},s=r[rn],o=r[sn];typeof s=="string"&&typeof o=="number"&&(e[n.name]={value:o,unit:s})}),e}class on{constructor(e={}){this._traceId=e.traceId||be(),this._spanId=e.spanId||Tt(),this._startTime=e.startTimestamp||Y(),this._links=e.links,this._attributes={},this.setAttributes({[B]:"manual",[Ue]:e.op,...e.attributes}),this._name=e.name,e.parentSpanId&&(this._parentSpanId=e.parentSpanId),"sampled"in e&&(this._sampled=e.sampled),e.endTimestamp&&(this._endTime=e.endTimestamp),this._events=[],this._isStandaloneSpan=e.isStandalone,this._endTime&&this._onSpanEnded()}addLink(e){return this._links?this._links.push(e):this._links=[e],this}addLinks(e){return this._links?this._links.push(...e):this._links=e,this}recordException(e,n){}spanContext(){const{_spanId:e,_traceId:n,_sampled:r}=this;return{spanId:e,traceId:n,traceFlags:r?fr:0}}setAttribute(e,n){return n===void 0?delete this._attributes[e]:this._attributes[e]=n,this}setAttributes(e){return Object.keys(e).forEach(n=>this.setAttribute(n,e[n])),this}updateStartTime(e){this._startTime=Le(e)}setStatus(e){return this._status=e,this}updateName(e){return this._name=e,this.setAttribute(le,"custom"),this}end(e){this._endTime||(this._endTime=Le(e),function(n){if(!C)return;const{description:r="< unknown name >",op:s="< unknown op >"}=F(n),{spanId:o}=n.spanContext(),i=`[Tracing] Finishing "${s}" ${V(n)===n?"root ":""}span "${r}" with ID ${o}`;w.log(i)}(this),this._onSpanEnded())}getSpanJSON(){return{data:this._attributes,description:this._name,op:this._attributes[Ue],parent_span_id:this._parentSpanId,span_id:this._spanId,start_timestamp:this._startTime,status:po(this._status),timestamp:this._endTime,trace_id:this._traceId,origin:this._attributes[B],profile_id:this._attributes[qn],exclusive_time:this._attributes[gt],measurements:Jr(this._events),is_segment:this._isStandaloneSpan&&V(this)===this||void 0,segment_id:this._isStandaloneSpan?V(this).spanContext().spanId:void 0,links:lo(this._links)}}isRecording(){return!this._endTime&&!!this._sampled}addEvent(e,n,r){C&&w.log("[Tracing] Adding an event to span:",e);const s=Gr(n)?n:r||Y(),o=Gr(n)?{}:n||{},i={name:e,time:Le(s),attributes:o};return this._events.push(i),this}isStandaloneSpan(){return!!this._isStandaloneSpan}_onSpanEnded(){const e=j();if(e&&e.emit("spanEnd",this),!(this._isStandaloneSpan||this===V(this)))return;if(this._isStandaloneSpan)return void(this._sampled?function(r){const s=j();if(!s)return;const o=r[1];if(!o||o.length===0)return void s.recordDroppedEvent("before_send","span");s.sendEnvelope(r)}(function(r,s){const o=xe(r[0]),i=s==null?void 0:s.getDsn(),a=s==null?void 0:s.getOptions().tunnel,c={sent_at:new Date().toISOString(),...function(f){return!!f.trace_id&&!!f.public_key}(o)&&{trace:o},...!!a&&i&&{dsn:vt(i)}},u=s==null?void 0:s.getOptions().beforeSendSpan,p=u?f=>{const l=F(f);return u(l)||(Hn(),l)}:F,h=[];for(const f of r){const l=p(f);l&&h.push(Ui(l))}return Ze(c,h)}([this],e)):(C&&w.log("[Tracing] Discarding standalone span because its trace was not chosen to be sampled."),e&&e.recordDroppedEvent("sample_rate","span")));const n=this._convertSpanToTransaction();n&&(Kt(this).scope||q()).captureEvent(n)}_convertSpanToTransaction(){var c;if(!Vr(F(this)))return;this._name||(C&&w.warn("Transaction has no name, falling back to `<unlabeled transaction>`."),this._name="<unlabeled transaction>");const{scope:e,isolationScope:n}=Kt(this),r=(c=e==null?void 0:e.getScopeData().sdkProcessingMetadata)==null?void 0:c.normalizedRequest;if(this._sampled!==!0)return;const s=Ft(this).filter(u=>u!==this&&!function(p){return p instanceof on&&p.isStandaloneSpan()}(u)).map(u=>F(u)).filter(Vr),o=this._attributes[le];delete this._attributes[Pr],s.forEach(u=>{delete u.data[Pr]});const i={contexts:{trace:Oi(this)},spans:s.length>1e3?s.sort((u,p)=>u.start_timestamp-p.start_timestamp).slice(0,1e3):s,start_timestamp:this._startTime,timestamp:this._endTime,transaction:this._name,type:"transaction",sdkProcessingMetadata:{capturedSpanScope:e,capturedSpanIsolationScope:n,dynamicSamplingContext:xe(this)},request:r,...o&&{transaction_info:{source:o}}},a=Jr(this._events);return a&&Object.keys(a).length&&(C&&w.log("[Measurements] Adding measurements to transaction event",JSON.stringify(a,void 0,2)),i.measurements=a),i}}function Gr(t){return t&&typeof t=="number"||t instanceof Date||Array.isArray(t)}function Vr(t){return!!(t.start_timestamp&&t.timestamp&&t.span_id&&t.trace_id)}const So="__SENTRY_SUPPRESS_TRACING__";function kt(t){const e=Eo();if(e.startInactiveSpan)return e.startInactiveSpan(t);const n=function(o){const i=o.experimental||{},a={isStandalone:i.standalone,...o};if(o.startTime){const c={...a};return c.startTimestamp=Le(o.startTime),delete c.startTime,c}return a}(t),{forceTransaction:r,parentSpan:s}=t;return(t.scope?o=>mt(t.scope,o):s!==void 0?o=>wo(s,o):o=>o())(()=>{const o=q(),i=function(a){const c=Vt(a);if(!c)return;const u=j();return(u?u.getOptions():{}).parentSpanIsAlwaysRootSpan?V(c):c}(o);return t.onlyIfParent&&!i?new Fe:function({parentSpan:a,spanArguments:c,forceTransaction:u,scope:p}){if(!Ee()){const l=new Fe;return(u||!a)&&Ut(l,{sampled:"false",sample_rate:"0",transaction:c.name,...xe(l)}),l}const h=ke();let f;if(a&&!u)f=function(l,d,m){const{spanId:g,traceId:b}=l.spanContext(),v=!d.getScopeData().sdkProcessingMetadata[So]&&Ne(l),E=v?new on({...m,parentSpanId:g,traceId:b,sampled:v}):new Fe({traceId:b});Lr(l,E);const _=j();return _&&(_.emit("spanStart",E),m.endTimestamp&&_.emit("spanEnd",E)),E}(a,p,c),Lr(a,f);else if(a){const l=xe(a),{traceId:d,spanId:m}=a.spanContext(),g=Ne(a);f=Yr({traceId:d,parentSpanId:m,...c},p,g),Ut(f,l)}else{const{traceId:l,dsc:d,parentSpanId:m,sampled:g}={...h.getPropagationContext(),...p.getPropagationContext()};f=Yr({traceId:l,parentSpanId:m,...c},p,g),d&&Ut(f,d)}return function(l){if(!C)return;const{description:d="< unknown name >",op:m="< unknown op >",parent_span_id:g}=F(l),{spanId:b}=l.spanContext(),v=Ne(l),E=V(l),_=E===l,y=`[Tracing] Starting ${v?"sampled":"unsampled"} ${_?"root ":""}span`,T=[`op: ${m}`,`name: ${d}`,`ID: ${b}`];if(g&&T.push(`parent ID: ${g}`),!_){const{op:x,description:D}=F(E);T.push(`root ID: ${E.spanContext().spanId}`),x&&T.push(`root op: ${x}`),D&&T.push(`root description: ${D}`)}w.log(`${y}
  ${T.join(`
  `)}`)}(f),function(l,d,m){l&&(Q(l,ao,m),Q(l,io,d))}(f,p,h),f}({parentSpan:i,spanArguments:n,forceTransaction:r,scope:o})})}function wo(t,e){const n=Eo();return n.withActiveSpan?n.withActiveSpan(t,e):mt(r=>(ht(r,t||void 0),e(r)))}function Eo(){return rt(ze())}function Yr(t,e,n){var d;const r=j(),s=(r==null?void 0:r.getOptions())||{},{name:o=""}=t,i={spanAttributes:{...t.attributes},spanName:o,parentSampled:n};r==null||r.emit("beforeSampling",i,{decision:!1});const a=i.parentSampled??n,c=i.spanAttributes,u=e.getPropagationContext(),[p,h,f]=e.getScopeData().sdkProcessingMetadata[So]?[!1]:function(m,g,b){if(!Ee(m))return[!1];let v,E;typeof m.tracesSampler=="function"?(E=m.tracesSampler({...g,inheritOrSampleWith:T=>typeof g.parentSampleRate=="number"?g.parentSampleRate:typeof g.parentSampled=="boolean"?Number(g.parentSampled):T}),v=!0):g.parentSampled!==void 0?E=g.parentSampled:m.tracesSampleRate!==void 0&&(E=m.tracesSampleRate,v=!0);const _=_t(E);if(_===void 0)return C&&w.warn(`[Tracing] Discarding root span because of invalid sample rate. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(E)} of type ${JSON.stringify(typeof E)}.`),[!1];if(!_)return C&&w.log("[Tracing] Discarding transaction because "+(typeof m.tracesSampler=="function"?"tracesSampler returned 0 or false":"a negative sampling decision was inherited or tracesSampleRate is set to 0")),[!1,_,v];const y=b<_;return y||C&&w.log(`[Tracing] Discarding transaction because it's not included in the random sample (sampling rate = ${Number(E)})`),[y,_,v]}(s,{name:o,parentSampled:a,attributes:c,parentSampleRate:_t((d=u.dsc)==null?void 0:d.sample_rate)},u.sampleRand),l=new on({...t,attributes:{[le]:"custom",[lr]:h!==void 0&&f?h:void 0,...c},sampled:p});return!p&&r&&(C&&w.log("[Tracing] Discarding root span because its trace was not chosen to be sampled."),r.recordDroppedEvent("sample_rate","transaction")),r&&r.emit("spanStart",l),l}const Ht={idleTimeout:1e3,finalTimeout:3e4,childSpanTimeout:15e3},Wi="heartbeatFailed",Ji="idleTimeout",Gi="finalTimeout",Vi="externalFinish";function Kr(t,e={}){const n=new Map;let r,s=!1,o=Vi,i=!e.disableAutoFinish;const a=[],{idleTimeout:c=Ht.idleTimeout,finalTimeout:u=Ht.finalTimeout,childSpanTimeout:p=Ht.childSpanTimeout,beforeSpanEnd:h}=e,f=j();if(!f||!Ee()){const _=new Fe;return Ut(_,{sample_rate:"0",sampled:"false",...xe(_)}),_}const l=q(),d=X(),m=function(_){const y=kt(_);return ht(q(),y),C&&w.log("[Tracing] Started span is an idle span"),y}(t);function g(){r&&(clearTimeout(r),r=void 0)}function b(_){g(),r=setTimeout(()=>{!s&&n.size===0&&i&&(o=Ji,m.end(_))},c)}function v(_){r=setTimeout(()=>{!s&&i&&(o=Wi,m.end(_))},p)}function E(_){s=!0,n.clear(),a.forEach(S=>S()),ht(l,d);const y=F(m),{start_timestamp:T}=y;if(!T)return;y.data[Yt]||m.setAttribute(Yt,o),w.log(`[Tracing] Idle span "${y.op}" finished`);const x=Ft(m).filter(S=>S!==m);let D=0;x.forEach(S=>{S.isRecording()&&(S.setStatus({code:H,message:"cancelled"}),S.end(_),C&&w.log("[Tracing] Cancelling span since span ended early",JSON.stringify(S,void 0,2)));const O=F(S),{timestamp:R=0,start_timestamp:k=0}=O,$=k<=_,P=R-k<=(u+c)/1e3;if(C){const I=JSON.stringify(S,void 0,2);$?P||w.log("[Tracing] Discarding span since it finished after idle span final timeout",I):w.log("[Tracing] Discarding span since it happened after idle span was finished",I)}P&&$||(function(I,M){I[Me]&&I[Me].delete(M)}(m,S),D++)}),D>0&&m.setAttribute("sentry.idle_span_discarded_spans",D)}return m.end=new Proxy(m.end,{apply(_,y,T){if(h&&h(m),y instanceof Fe)return;const[x,...D]=T,S=Le(x||Y()),O=Ft(m).filter(I=>I!==m);if(!O.length)return E(S),Reflect.apply(_,y,[S,...D]);const R=O.map(I=>F(I).timestamp).filter(I=>!!I),k=R.length?Math.max(...R):void 0,$=F(m).start_timestamp,P=Math.min($?$+u/1e3:1/0,Math.max($||-1/0,Math.min(S,k||1/0)));return E(P),Reflect.apply(_,y,[P,...D])}}),a.push(f.on("spanStart",_=>{if(!(s||_===m||F(_).timestamp)){var y;Ft(m).includes(_)&&(y=_.spanContext().spanId,g(),n.set(y,!0),v(Y()+p/1e3))}})),a.push(f.on("spanEnd",_=>{var y;s||(y=_.spanContext().spanId,n.has(y)&&n.delete(y),n.size===0&&b(Y()+c/1e3))})),a.push(f.on("idleSpanEnableAutoFinish",_=>{_===m&&(i=!0,b(),n.size&&v())})),e.disableAutoFinish||b(),setTimeout(()=>{s||(m.setStatus({code:H,message:"deadline_exceeded"}),o=Gi,m.end())},u),m}var ce;function He(t){return new Te(e=>{e(t)})}function Xt(t){return new Te((e,n)=>{n(t)})}(function(t){t[t.PENDING=0]="PENDING",t[t.RESOLVED=1]="RESOLVED",t[t.REJECTED=2]="REJECTED"})(ce||(ce={}));class Te{constructor(e){this._state=ce.PENDING,this._handlers=[],this._runExecutor(e)}then(e,n){return new Te((r,s)=>{this._handlers.push([!1,o=>{if(e)try{r(e(o))}catch(i){s(i)}else r(o)},o=>{if(n)try{r(n(o))}catch(i){s(i)}else s(o)}]),this._executeHandlers()})}catch(e){return this.then(n=>n,e)}finally(e){return new Te((n,r)=>{let s,o;return this.then(i=>{o=!1,s=i,e&&e()},i=>{o=!0,s=i,e&&e()}).then(()=>{o?r(s):n(s)})})}_executeHandlers(){if(this._state===ce.PENDING)return;const e=this._handlers.slice();this._handlers=[],e.forEach(n=>{n[0]||(this._state===ce.RESOLVED&&n[1](this._value),this._state===ce.REJECTED&&n[2](this._value),n[0]=!0)})}_runExecutor(e){const n=(o,i)=>{this._state===ce.PENDING&&(nn(i)?i.then(r,s):(this._state=o,this._value=i,this._executeHandlers()))},r=o=>{n(ce.RESOLVED,o)},s=o=>{n(ce.REJECTED,o)};try{e(r,s)}catch(o){s(o)}}}function Jn(t,e,n,r=0){return new Te((s,o)=>{const i=t[r];if(e===null||typeof i!="function")s(e);else{const a=i({...e},n);C&&i.id&&a===null&&w.log(`Event processor "${i.id}" dropped event`),nn(a)?a.then(c=>Jn(t,c,n,r+1).then(s)).then(null,o):Jn(t,a,n,r+1).then(s).then(null,o)}})}let Pt,Xr,mn;function Yi(t,e){const{fingerprint:n,span:r,breadcrumbs:s,sdkProcessingMetadata:o}=e;(function(i,a){const{extra:c,tags:u,user:p,contexts:h,level:f,transactionName:l}=a;Object.keys(c).length&&(i.extra={...c,...i.extra}),Object.keys(u).length&&(i.tags={...u,...i.tags}),Object.keys(p).length&&(i.user={...p,...i.user}),Object.keys(h).length&&(i.contexts={...h,...i.contexts}),f&&(i.level=f),l&&i.type!=="transaction"&&(i.transaction=l)})(t,e),r&&function(i,a){i.contexts={trace:Ri(a),...i.contexts},i.sdkProcessingMetadata={dynamicSamplingContext:xe(a),...i.sdkProcessingMetadata};const c=V(a),u=F(c).description;u&&!i.transaction&&i.type==="transaction"&&(i.transaction=u)}(t,r),function(i,a){i.fingerprint=i.fingerprint?Array.isArray(i.fingerprint)?i.fingerprint:[i.fingerprint]:[],a&&(i.fingerprint=i.fingerprint.concat(a)),i.fingerprint.length||delete i.fingerprint}(t,n),function(i,a){const c=[...i.breadcrumbs||[],...a];i.breadcrumbs=c.length?c:void 0}(t,s),function(i,a){i.sdkProcessingMetadata={...i.sdkProcessingMetadata,...a}}(t,o)}function Zr(t,e){const{extra:n,tags:r,user:s,contexts:o,level:i,sdkProcessingMetadata:a,breadcrumbs:c,fingerprint:u,eventProcessors:p,attachments:h,propagationContext:f,transactionName:l,span:d}=e;At(t,"extra",n),At(t,"tags",r),At(t,"user",s),At(t,"contexts",o),t.sdkProcessingMetadata=xt(t.sdkProcessingMetadata,a,2),i&&(t.level=i),l&&(t.transactionName=l),d&&(t.span=d),c.length&&(t.breadcrumbs=[...t.breadcrumbs,...c]),u.length&&(t.fingerprint=[...t.fingerprint,...u]),p.length&&(t.eventProcessors=[...t.eventProcessors,...p]),h.length&&(t.attachments=[...t.attachments,...h]),t.propagationContext={...t.propagationContext,...f}}function At(t,e,n){t[e]=xt(t[e],n,1)}function Ki(t,e,n,r,s,o){const{normalizeDepth:i=3,normalizeMaxBreadth:a=1e3}=t,c={...e,event_id:e.event_id||n.event_id||ne(),timestamp:e.timestamp||Et()},u=n.integrations||t.integrations.map(d=>d.name);(function(d,m){const{environment:g,release:b,dist:v,maxValueLength:E=250}=m;d.environment=d.environment||g||hr,!d.release&&b&&(d.release=b),!d.dist&&v&&(d.dist=v);const _=d.request;_!=null&&_.url&&(_.url=Gt(_.url,E))})(c,t),function(d,m){m.length>0&&(d.sdk=d.sdk||{},d.sdk.integrations=[...d.sdk.integrations||[],...m])}(c,u),s&&s.emit("applyFrameMetadata",e),e.type===void 0&&function(d,m){var b,v;const g=function(E){const _=N._sentryDebugIds;if(!_)return{};const y=Object.keys(_);return mn&&y.length===Xr||(Xr=y.length,mn=y.reduce((T,x)=>{Pt||(Pt={});const D=Pt[x];if(D)T[D[0]]=D[1];else{const S=E(x);for(let O=S.length-1;O>=0;O--){const R=S[O],k=R==null?void 0:R.filename,$=_[x];if(k&&$){T[k]=$,Pt[x]=[k,$];break}}}return T},{})),mn}(m);(v=(b=d.exception)==null?void 0:b.values)==null||v.forEach(E=>{var _,y;(y=(_=E.stacktrace)==null?void 0:_.frames)==null||y.forEach(T=>{T.filename&&(T.debug_id=g[T.filename])})})}(c,t.stackParser);const p=function(d,m){if(!m)return d;const g=d?d.clone():new ge;return g.update(m),g}(r,n.captureContext);n.mechanism&&Ye(c,n.mechanism);const h=s?s.getEventProcessors():[],f=Wt("globalScope",()=>new ge).getScopeData();o&&Zr(f,o.getScopeData()),p&&Zr(f,p.getScopeData());const l=[...n.attachments||[],...f.attachments];return l.length&&(n.attachments=l),Yi(c,f),Jn([...h,...f.eventProcessors],c,n).then(d=>(d&&function(m){var v,E;const g={};if((E=(v=m.exception)==null?void 0:v.values)==null||E.forEach(_=>{var y,T;(T=(y=_.stacktrace)==null?void 0:y.frames)==null||T.forEach(x=>{x.debug_id&&(x.abs_path?g[x.abs_path]=x.debug_id:x.filename&&(g[x.filename]=x.debug_id),delete x.debug_id)})}),Object.keys(g).length===0)return;m.debug_meta=m.debug_meta||{},m.debug_meta.images=m.debug_meta.images||[];const b=m.debug_meta.images;Object.entries(g).forEach(([_,y])=>{b.push({type:"sourcemap",code_file:_,debug_id:y})})}(d),typeof i=="number"&&i>0?function(m,g,b){var E,_;if(!m)return null;const v={...m,...m.breadcrumbs&&{breadcrumbs:m.breadcrumbs.map(y=>({...y,...y.data&&{data:ae(y.data,g,b)}}))},...m.user&&{user:ae(m.user,g,b)},...m.contexts&&{contexts:ae(m.contexts,g,b)},...m.extra&&{extra:ae(m.extra,g,b)}};return(E=m.contexts)!=null&&E.trace&&v.contexts&&(v.contexts.trace=m.contexts.trace,m.contexts.trace.data&&(v.contexts.trace.data=ae(m.contexts.trace.data,g,b))),m.spans&&(v.spans=m.spans.map(y=>({...y,...y.data&&{data:ae(y.data,g,b)}}))),(_=m.contexts)!=null&&_.flags&&v.contexts&&(v.contexts.flags=ae(m.contexts.flags,3,b)),v}(d,i,a):d))}function Gn(t,e){return q().captureException(t,void 0)}function Qr(t,e){return q().captureEvent(t,e)}function es(t){const e=ke(),n=q(),{userAgent:r}=N.navigator||{},s=Si({user:n.getUser()||e.getUser(),...r&&{userAgent:r},...t}),o=e.getSession();return(o==null?void 0:o.status)==="ok"&&Ke(o,{status:"exited"}),xo(),e.setSession(s),s}function xo(){const t=ke(),e=q().getSession()||t.getSession();e&&function(n,r){let s={};n.status==="ok"&&(s={status:"exited"}),Ke(n,s)}(e),To(),t.setSession()}function To(){const t=ke(),e=j(),n=t.getSession();n&&e&&e.captureSession(n)}function ts(t=!1){t?xo():To()}const Xi="7";function Zi(t,e,n){return e||`${function(r){return`${function(s){const o=s.protocol?`${s.protocol}:`:"",i=s.port?`:${s.port}`:"";return`${o}//${s.host}${i}${s.path?`/${s.path}`:""}/api/`}(r)}${r.projectId}/envelope/`}(t)}?${function(r,s){const o={sentry_version:Xi};return r.publicKey&&(o.sentry_key=r.publicKey),s&&(o.sentry_client=`${s.name}/${s.version}`),new URLSearchParams(o).toString()}(t,n)}`}const ns=[];function Qi(t){const e=t.defaultIntegrations||[],n=t.integrations;let r;if(e.forEach(s=>{s.isDefaultInstance=!0}),Array.isArray(n))r=[...e,...n];else if(typeof n=="function"){const s=n(e);r=Array.isArray(s)?s:[s]}else r=e;return function(s){const o={};return s.forEach(i=>{const{name:a}=i,c=o[a];c&&!c.isDefaultInstance&&i.isDefaultInstance||(o[a]=i)}),Object.values(o)}(r)}function rs(t,e){for(const n of e)n!=null&&n.afterAllSetup&&n.afterAllSetup(t)}function ss(t,e,n){if(n[e.name])C&&w.log(`Integration skipped because it was already installed: ${e.name}`);else{if(n[e.name]=e,ns.indexOf(e.name)===-1&&typeof e.setupOnce=="function"&&(e.setupOnce(),ns.push(e.name)),e.setup&&typeof e.setup=="function"&&e.setup(t),typeof e.preprocessEvent=="function"){const r=e.preprocessEvent.bind(e);t.on("preprocessEvent",(s,o)=>r(s,o,t))}if(typeof e.processEvent=="function"){const r=e.processEvent.bind(e),s=Object.assign((o,i)=>r(o,i,t),{id:e.name});t.addEventProcessor(s)}C&&w.log(`Integration installed: ${e.name}`)}}function ko(t){const e=[];t.message&&e.push(t.message);try{const n=t.exception.values[t.exception.values.length-1];n!=null&&n.value&&(e.push(n.value),n.type&&e.push(`${n.type}: ${n.value}`))}catch{}return e}const os="Not capturing exception because it's already been captured.",is="Discarded session because of missing or non-string release",Co=Symbol.for("SentryInternalError"),Io=Symbol.for("SentryDoNotSendEventError");function Ot(t){return{message:t,[Co]:!0}}function gn(t){return{message:t,[Io]:!0}}function as(t){return!!t&&typeof t=="object"&&Co in t}function cs(t){return!!t&&typeof t=="object"&&Io in t}class ea{constructor(e){if(this._options=e,this._integrations={},this._numProcessing=0,this._outcomes={},this._hooks={},this._eventProcessors=[],e.dsn?this._dsn=ji(e.dsn):C&&w.warn("No DSN provided, client will not send events."),this._dsn){const n=Zi(this._dsn,e.tunnel,e._metadata?e._metadata.sdk:void 0);this._transport=e.transport({tunnel:this._options.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this),...e.transportOptions,url:n})}}captureException(e,n,r){const s=ne();if(Ir(e))return C&&w.log(os),s;const o={event_id:s,...n};return this._process(this.eventFromException(e,o).then(i=>this._captureEvent(i,o,r))),o.event_id}captureMessage(e,n,r,s){const o={event_id:ne(),...r},i=cr(e)?e:String(e),a=pt(e)?this.eventFromMessage(i,n,o):this.eventFromException(e,o);return this._process(a.then(c=>this._captureEvent(c,o,s))),o.event_id}captureEvent(e,n,r){const s=ne();if(n!=null&&n.originalException&&Ir(n.originalException))return C&&w.log(os),s;const o={event_id:s,...n},i=e.sdkProcessingMetadata||{},a=i.capturedSpanScope,c=i.capturedSpanIsolationScope;return this._process(this._captureEvent(e,o,a||r,c)),o.event_id}captureSession(e){this.sendSession(e),Ke(e,{init:!1})}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(e){const n=this._transport;return n?(this.emit("flush"),this._isClientDoneProcessing(e).then(r=>n.flush(e).then(s=>r&&s))):He(!0)}close(e){return this.flush(e).then(n=>(this.getOptions().enabled=!1,this.emit("close"),n))}getEventProcessors(){return this._eventProcessors}addEventProcessor(e){this._eventProcessors.push(e)}init(){(this._isEnabled()||this._options.integrations.some(({name:e})=>e.startsWith("Spotlight")))&&this._setupIntegrations()}getIntegrationByName(e){return this._integrations[e]}addIntegration(e){const n=this._integrations[e.name];ss(this,e,this._integrations),n||rs(this,[e])}sendEvent(e,n={}){this.emit("beforeSendEvent",e,n);let r=zi(e,this._dsn,this._options._metadata,this._options.tunnel);for(const o of n.attachments||[])r=Fi(r,Hi(o));const s=this.sendEnvelope(r);s&&s.then(o=>this.emit("afterSendEvent",e,o),null)}sendSession(e){const{release:n,environment:r=hr}=this._options;if("aggregates"in e){const o=e.attrs||{};if(!o.release&&!n)return void(C&&w.warn(is));o.release=o.release||n,o.environment=o.environment||r,e.attrs=o}else{if(!e.release&&!n)return void(C&&w.warn(is));e.release=e.release||n,e.environment=e.environment||r}this.emit("beforeSendSession",e);const s=function(o,i,a,c){const u=bo(a);return Ze({sent_at:new Date().toISOString(),...u&&{sdk:u},...!!c&&i&&{dsn:vt(i)}},["aggregates"in o?[{type:"sessions"},o]:[{type:"session"},o.toJSON()]])}(e,this._dsn,this._options._metadata,this._options.tunnel);this.sendEnvelope(s)}recordDroppedEvent(e,n,r=1){if(this._options.sendClientReports){const s=`${e}:${n}`;C&&w.log(`Recording outcome: "${s}"${r>1?` (${r} times)`:""}`),this._outcomes[s]=(this._outcomes[s]||0)+r}}on(e,n){const r=this._hooks[e]=this._hooks[e]||[];return r.push(n),()=>{const s=r.indexOf(n);s>-1&&r.splice(s,1)}}emit(e,...n){const r=this._hooks[e];r&&r.forEach(s=>s(...n))}sendEnvelope(e){return this.emit("beforeEnvelope",e),this._isEnabled()&&this._transport?this._transport.send(e).then(null,n=>(C&&w.error("Error while sending envelope:",n),n)):(C&&w.error("Transport disabled"),He({}))}_setupIntegrations(){const{integrations:e}=this._options;this._integrations=function(n,r){const s={};return r.forEach(o=>{o&&ss(n,o,s)}),s}(this,e),rs(this,e)}_updateSessionFromEvent(e,n){var a;let r=n.level==="fatal",s=!1;const o=(a=n.exception)==null?void 0:a.values;if(o){s=!0;for(const c of o){const u=c.mechanism;if((u==null?void 0:u.handled)===!1){r=!0;break}}}const i=e.status==="ok";(i&&e.errors===0||i&&r)&&(Ke(e,{...r&&{status:"crashed"},errors:e.errors||Number(s||r)}),this.captureSession(e))}_isClientDoneProcessing(e){return new Te(n=>{let r=0;const s=setInterval(()=>{this._numProcessing==0?(clearInterval(s),n(!0)):(r+=1,e&&r>=e&&(clearInterval(s),n(!1)))},1)})}_isEnabled(){return this.getOptions().enabled!==!1&&this._transport!==void 0}_prepareEvent(e,n,r,s){const o=this.getOptions(),i=Object.keys(this._integrations);return!n.integrations&&(i!=null&&i.length)&&(n.integrations=i),this.emit("preprocessEvent",e,n),e.type||s.setLastEventId(e.event_id||n.event_id),Ki(o,e,n,r,this,s).then(a=>{if(a===null)return a;this.emit("postprocessEvent",a,n),a.contexts={trace:Ti(r),...a.contexts};const c=vo(this,r);return a.sdkProcessingMetadata={dynamicSamplingContext:c,...a.sdkProcessingMetadata},a})}_captureEvent(e,n={},r=q(),s=ke()){return C&&_n(e)&&w.log(`Captured error event \`${ko(e)[0]||"<unknown>"}\``),this._processEvent(e,n,r,s).then(o=>o.event_id,o=>{C&&(cs(o)?w.log(o.message):as(o)?w.warn(o.message):w.warn(o))})}_processEvent(e,n,r,s){const o=this.getOptions(),{sampleRate:i}=o,a=us(e),c=_n(e),u=e.type||"error",p=`before send for type \`${u}\``,h=i===void 0?void 0:_t(i);if(c&&typeof h=="number"&&Math.random()>h)return this.recordDroppedEvent("sample_rate","error"),Xt(gn(`Discarding event because it's not included in the random sample (sampling rate = ${i})`));const f=u==="replay_event"?"replay":u;return this._prepareEvent(e,n,r,s).then(l=>{if(l===null)throw this.recordDroppedEvent("event_processor",f),gn("An event processor returned `null`, will not send event.");if(n.data&&n.data.__sentry__===!0)return l;const d=function(m,g,b,v){const{beforeSend:E,beforeSendTransaction:_,beforeSendSpan:y}=g;let T=b;if(_n(T)&&E)return E(T,v);if(us(T)){if(y){const D=y(function(S){var z;const{trace_id:O,parent_span_id:R,span_id:k,status:$,origin:P,data:I,op:M}=((z=S.contexts)==null?void 0:z.trace)??{};return{data:I??{},description:S.transaction,op:M,parent_span_id:R,span_id:k??"",start_timestamp:S.start_timestamp??0,status:$,timestamp:S.timestamp,trace_id:O??"",origin:P,profile_id:I==null?void 0:I[qn],exclusive_time:I==null?void 0:I[gt],measurements:S.measurements,is_segment:!0}}(T));if(D?T=xt(b,{type:"transaction",timestamp:(x=D).timestamp,start_timestamp:x.start_timestamp,transaction:x.description,contexts:{trace:{trace_id:x.trace_id,span_id:x.span_id,parent_span_id:x.parent_span_id,op:x.op,status:x.status,origin:x.origin,data:{...x.data,...x.profile_id&&{[qn]:x.profile_id},...x.exclusive_time&&{[gt]:x.exclusive_time}}}},measurements:x.measurements}):Hn(),T.spans){const S=[];for(const O of T.spans){const R=y(O);R?S.push(R):(Hn(),S.push(O))}T.spans=S}}if(_){if(T.spans){const D=T.spans.length;T.sdkProcessingMetadata={...b.sdkProcessingMetadata,spanCountBeforeProcessing:D}}return _(T,v)}}var x;return T}(0,o,l,n);return function(m,g){const b=`${g} must return \`null\` or a valid event.`;if(nn(m))return m.then(v=>{if(!ft(v)&&v!==null)throw Ot(b);return v},v=>{throw Ot(`${g} rejected with ${v}`)});if(!ft(m)&&m!==null)throw Ot(b);return m}(d,p)}).then(l=>{var g;if(l===null){if(this.recordDroppedEvent("before_send",f),a){const b=1+(e.spans||[]).length;this.recordDroppedEvent("before_send","span",b)}throw gn(`${p} returned \`null\`, will not send event.`)}const d=r.getSession()||s.getSession();if(c&&d&&this._updateSessionFromEvent(d,l),a){const b=(((g=l.sdkProcessingMetadata)==null?void 0:g.spanCountBeforeProcessing)||0)-(l.spans?l.spans.length:0);b>0&&this.recordDroppedEvent("before_send","span",b)}const m=l.transaction_info;if(a&&m&&l.transaction!==e.transaction){const b="custom";l.transaction_info={...m,source:b}}return this.sendEvent(l,n),l}).then(null,l=>{throw cs(l)||as(l)?l:(this.captureException(l,{data:{__sentry__:!0},originalException:l}),Ot(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.
Reason: ${l}`))})}_process(e){this._numProcessing++,e.then(n=>(this._numProcessing--,n),n=>(this._numProcessing--,n))}_clearOutcomes(){const e=this._outcomes;return this._outcomes={},Object.entries(e).map(([n,r])=>{const[s,o]=n.split(":");return{reason:s,category:o,quantity:r}})}_flushOutcomes(){C&&w.log("Flushing outcomes...");const e=this._clearOutcomes();if(e.length===0)return void(C&&w.log("No outcomes to send"));if(!this._dsn)return void(C&&w.log("No dsn provided, will not send outcomes"));C&&w.log("Sending outcomes:",e);const n=(r=e,Ze((s=this._options.tunnel&&vt(this._dsn))?{dsn:s}:{},[[{type:"client_report"},{timestamp:Et(),discarded_events:r}]]));var r,s;this.sendEnvelope(n)}}function _n(t){return t.type===void 0}function us(t){return t.type==="transaction"}function vn(t,e){var o;const n=function(i){var a;return(a=N._sentryClientToLogBufferMap)==null?void 0:a.get(i)}(t)??[];if(n.length===0)return;const r=t.getOptions(),s=function(i,a,c,u){const p={};return a!=null&&a.sdk&&(p.sdk={name:a.sdk.name,version:a.sdk.version}),c&&u&&(p.dsn=vt(u)),Ze(p,[(h=i,[{type:"log",item_count:h.length,content_type:"application/vnd.sentry.items.log+json"},{items:h}])]);var h}(n,r._metadata,r.tunnel,t.getDsn());(o=N._sentryClientToLogBufferMap)==null||o.set(t,[]),t.emit("flushLogs"),t.sendEnvelope(s)}function ta(t,e){e.debug===!0&&(C?w.enable():We(()=>{console.warn("[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle.")})),q().update(e.initialScope);const n=new t(e);return function(r){q().setClient(r)}(n),n.init(),n}N._sentryClientToLogBufferMap=new WeakMap;const $o=Symbol.for("SentryBufferFullError");function na(t){const e=[];function n(r){return e.splice(e.indexOf(r),1)[0]||Promise.resolve(void 0)}return{$:e,add:function(r){if(!(t===void 0||e.length<t))return Xt($o);const s=r();return e.indexOf(s)===-1&&e.push(s),s.then(()=>n(s)).then(null,()=>n(s).then(null,()=>{})),s},drain:function(r){return new Te((s,o)=>{let i=e.length;if(!i)return s(!0);const a=setTimeout(()=>{r&&r>0&&s(!1)},r);e.forEach(c=>{He(c).then(()=>{--i||(clearTimeout(a),s(!0))},o)})})}}}const ra=6e4;function sa(t,{statusCode:e,headers:n},r=Date.now()){const s={...t},o=n==null?void 0:n["x-sentry-rate-limits"],i=n==null?void 0:n["retry-after"];if(o)for(const a of o.trim().split(",")){const[c,u,,,p]=a.split(":",5),h=parseInt(c,10),f=1e3*(isNaN(h)?60:h);if(u)for(const l of u.split(";"))l==="metric_bucket"&&p&&!p.split(";").includes("custom")||(s[l]=r+f);else s.all=r+f}else i?s.all=r+function(a,c=Date.now()){const u=parseInt(`${a}`,10);if(!isNaN(u))return 1e3*u;const p=Date.parse(`${a}`);return isNaN(p)?ra:p-c}(i,r):e===429&&(s.all=r+6e4);return s}const oa=64;function ia(t,e,n=na(t.bufferSize||oa)){let r={};return{send:function(s){const o=[];if(zr(s,(c,u)=>{const p=Wr(u);(function(h,f,l=Date.now()){return function(d,m){return d[m]||d.all||0}(h,f)>l})(r,p)?t.recordDroppedEvent("ratelimit_backoff",p):o.push(c)}),o.length===0)return He({});const i=Ze(s[0],o),a=c=>{zr(i,(u,p)=>{t.recordDroppedEvent(c,Wr(p))})};return n.add(()=>e({body:qi(i)}).then(c=>(c.statusCode!==void 0&&(c.statusCode<200||c.statusCode>=300)&&C&&w.warn(`Sentry responded with status code ${c.statusCode} to sent event.`),r=sa(r,c),c),c=>{throw a("network_error"),C&&w.error("Encountered error running transport request:",c),c})).then(c=>c,c=>{if(c===$o)return C&&w.error("Skipped sending event because buffer is full."),a("queue_overflow"),He({});throw c})},flush:s=>n.drain(s)}}function aa(t){var e;((e=t.user)==null?void 0:e.ip_address)===void 0&&(t.user={...t.user,ip_address:"{{auto}}"})}function ca(t){var e;"aggregates"in t?((e=t.attrs)==null?void 0:e.ip_address)===void 0&&(t.attrs={...t.attrs,ip_address:"{{auto}}"}):t.ipAddress===void 0&&(t.ipAddress="{{auto}}")}function Po(t,e,n=[e],r="npm"){const s=t._metadata||{};s.sdk||(s.sdk={name:`sentry.javascript.${e}`,packages:n.map(o=>({name:`${r}:@sentry/${o}`,version:De})),version:De}),t._metadata=s}function Ao(t={}){const e=j();if(!function(){const a=j();return(a==null?void 0:a.getOptions().enabled)!==!1&&!!(a!=null&&a.getTransport())}()||!e)return{};const n=rt(ze());if(n.getTraceData)return n.getTraceData(t);const r=q(),s=t.span||X(),o=s?function(a){const{traceId:c,spanId:u}=a.spanContext();return Or(c,u,Ne(a))}(s):function(a){const{traceId:c,sampled:u,propagationSpanId:p}=a.getPropagationContext();return Or(c,p,u)}(r),i=Pi(s?xe(s):vo(e,r));return uo.test(o)?{"sentry-trace":o,baggage:i}:(w.warn("Invalid sentry-trace data. Cannot generate trace data"),{})}const ua=100;function Ie(t,e){const n=j(),r=ke();if(!n)return;const{beforeBreadcrumb:s=null,maxBreadcrumbs:o=ua}=n.getOptions();if(o<=0)return;const i={timestamp:Et(),...t},a=s?We(()=>s(i,e)):i;a!==null&&(n.emit&&n.emit("beforeAddBreadcrumb",a,e),r.addBreadcrumb(a,o))}let ds;const ls=new WeakMap,da=()=>({name:"FunctionToString",setupOnce(){ds=Function.prototype.toString;try{Function.prototype.toString=function(...t){const e=dr(this),n=ls.has(j())&&e!==void 0?e:this;return ds.apply(n,t)}}catch{}},setup(t){ls.set(t,!0)}}),la=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop completed with undelivered notifications.$/,/^Cannot redefine property: googletag$/,/^Can't find variable: gmo$/,/^undefined is not an object \(evaluating 'a\.[A-Z]'\)$/,`can't redefine non-configurable property "solana"`,"vv().getRestrictions is not a function. (In 'vv().getRestrictions(1,a)', 'vv().getRestrictions' is undefined)","Can't find variable: _AutofillCallbackHandler",/^Non-Error promise rejection captured with value: Object Not Found Matching Id:\d+, MethodName:simulateEvent, ParamCount:\d+$/,/^Java exception was raised during method invocation$/],pa=(t={})=>{let e;return{name:"EventFilters",setup(n){const r=n.getOptions();e=ps(t,r)},processEvent(n,r,s){if(!e){const o=s.getOptions();e=ps(t,o)}return function(o,i){if(o.type){if(o.type==="transaction"&&function(a,c){if(!(c!=null&&c.length))return!1;const u=a.transaction;return!!u&&Oe(u,c)}(o,i.ignoreTransactions))return C&&w.warn(`Event dropped due to being matched by \`ignoreTransactions\` option.
Event: ${Pe(o)}`),!0}else{if(function(a,c){return c!=null&&c.length?ko(a).some(u=>Oe(u,c)):!1}(o,i.ignoreErrors))return C&&w.warn(`Event dropped due to being matched by \`ignoreErrors\` option.
Event: ${Pe(o)}`),!0;if(function(a){var c,u;return(u=(c=a.exception)==null?void 0:c.values)!=null&&u.length?!a.message&&!a.exception.values.some(p=>p.stacktrace||p.type&&p.type!=="Error"||p.value):!1}(o))return C&&w.warn(`Event dropped due to not having an error message, error type or stacktrace.
Event: ${Pe(o)}`),!0;if(function(a,c){if(!(c!=null&&c.length))return!1;const u=Rt(a);return!!u&&Oe(u,c)}(o,i.denyUrls))return C&&w.warn(`Event dropped due to being matched by \`denyUrls\` option.
Event: ${Pe(o)}.
Url: ${Rt(o)}`),!0;if(!function(a,c){if(!(c!=null&&c.length))return!0;const u=Rt(a);return!u||Oe(u,c)}(o,i.allowUrls))return C&&w.warn(`Event dropped due to not being matched by \`allowUrls\` option.
Event: ${Pe(o)}.
Url: ${Rt(o)}`),!0}return!1}(n,e)?null:n}}},fa=(t={})=>({...pa(t),name:"InboundFilters"});function ps(t={},e={}){return{allowUrls:[...t.allowUrls||[],...e.allowUrls||[]],denyUrls:[...t.denyUrls||[],...e.denyUrls||[]],ignoreErrors:[...t.ignoreErrors||[],...e.ignoreErrors||[],...t.disableErrorDefaults?[]:la],ignoreTransactions:[...t.ignoreTransactions||[],...e.ignoreTransactions||[]]}}function Rt(t){var e,n;try{const r=[...((e=t.exception)==null?void 0:e.values)??[]].reverse().find(o=>{var i,a,c;return((i=o.mechanism)==null?void 0:i.parent_id)===void 0&&((c=(a=o.stacktrace)==null?void 0:a.frames)==null?void 0:c.length)}),s=(n=r==null?void 0:r.stacktrace)==null?void 0:n.frames;return s?function(o=[]){for(let i=o.length-1;i>=0;i--){const a=o[i];if(a&&a.filename!=="<anonymous>"&&a.filename!=="[native code]")return a.filename||null}return null}(s):null}catch{return C&&w.error(`Cannot extract url for event ${Pe(t)}`),null}}function ha(t,e,n,r,s,o){var a;if(!((a=s.exception)!=null&&a.values)||!o||!me(o.originalException,Error))return;const i=s.exception.values.length>0?s.exception.values[s.exception.values.length-1]:void 0;i&&(s.exception.values=Vn(t,e,r,o.originalException,n,s.exception.values,i,0))}function Vn(t,e,n,r,s,o,i,a){if(o.length>=n+1)return o;let c=[...o];if(me(r[s],Error)){fs(i,a);const u=t(e,r[s]),p=c.length;hs(u,s,p,a),c=Vn(t,e,n,r[s],s,[u,...c],u,p)}return Array.isArray(r.errors)&&r.errors.forEach((u,p)=>{if(me(u,Error)){fs(i,a);const h=t(e,u),f=c.length;hs(h,`errors[${p}]`,f,a),c=Vn(t,e,n,u,s,[h,...c],h,f)}}),c}function fs(t,e){t.mechanism=t.mechanism||{type:"generic",handled:!0},t.mechanism={...t.mechanism,...t.type==="AggregateError"&&{is_exception_group:!0},exception_id:e}}function hs(t,e,n,r){t.mechanism=t.mechanism||{type:"generic",handled:!0},t.mechanism={...t.mechanism,type:"chained",source:e,exception_id:n,parent_id:r}}function ma(){"console"in N&&Mn.forEach(function(t){t in N.console&&Z(N.console,t,function(e){return Jt[t]=e,function(...n){re("console",{args:n,level:t});const r=Jt[t];r==null||r.apply(N.console,n)}})})}function ga(t){return t==="warn"?"warning":["fatal","error","warning","log","info","debug"].includes(t)?t:"log"}const _a=()=>{let t;return{name:"Dedupe",processEvent(e){if(e.type)return e;try{if(function(n,r){return r?!!(function(s,o){const i=s.message,a=o.message;return!(!i&&!a||i&&!a||!i&&a||i!==a||!gs(s,o)||!ms(s,o))}(n,r)||function(s,o){const i=_s(o),a=_s(s);return!(!i||!a||i.type!==a.type||i.value!==a.value||!gs(s,o)||!ms(s,o))}(n,r)):!1}(e,t))return C&&w.warn("Event dropped due to being a duplicate of previously captured event."),null}catch{}return t=e}}};function ms(t,e){let n=qr(t),r=qr(e);if(!n&&!r)return!0;if(n&&!r||!n&&r||r.length!==n.length)return!1;for(let s=0;s<r.length;s++){const o=r[s],i=n[s];if(o.filename!==i.filename||o.lineno!==i.lineno||o.colno!==i.colno||o.function!==i.function)return!1}return!0}function gs(t,e){let n=t.fingerprint,r=e.fingerprint;if(!n&&!r)return!0;if(n&&!r||!n&&r)return!1;try{return n.join("")===r.join("")}catch{return!1}}function _s(t){var e;return((e=t.exception)==null?void 0:e.values)&&t.exception.values[0]}const va="thismessage:/";function Oo(t){return"isRelative"in t}function Ro(t,e){const n=t.indexOf("://")<=0&&t.indexOf("//")!==0,r=n?va:void 0;try{if("canParse"in URL&&!URL.canParse(t,r))return;const s=new URL(t,r);return n?{isRelative:n,pathname:s.pathname,search:s.search,hash:s.hash}:s}catch{}}function ya(t){if(Oo(t))return t.pathname;const e=new URL(t);return e.search="",e.hash="",["80","443"].includes(e.port)&&(e.port=""),e.password&&(e.password="%filtered%"),e.username&&(e.username="%filtered%"),e.toString()}function Ve(t){if(!t)return{};const e=t.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!e)return{};const n=e[6]||"",r=e[8]||"";return{host:e[4],path:e[5],protocol:e[2],search:n,hash:r,relative:e[5]+n+r}}function ba(t,e,n,r,s="auto.http.browser"){if(!t.fetchData)return;const{method:o,url:i}=t.fetchData,a=Ee()&&e(i);if(t.endTimestamp&&a){const h=t.fetchData.__span;if(!h)return;const f=r[h];return void(f&&(function(l,d){var m;if(d.response){oo(l,d.response.status);const g=((m=d.response)==null?void 0:m.headers)&&d.response.headers.get("content-length");if(g){const b=parseInt(g);b>0&&l.setAttribute("http.response_content_length",b)}}else d.error&&l.setStatus({code:H,message:"internal_error"});l.end()}(f,t),delete r[h]))}const c=!!X(),u=a&&c?kt(function(h,f,l){const d=Ro(h);return{name:d?`${f} ${ya(d)}`:f,attributes:Sa(h,d,f,l)}}(i,o,s)):new Fe;if(t.fetchData.__span=u.spanContext().spanId,r[u.spanContext().spanId]=u,n(t.fetchData.url)){const h=t.args[0],f=t.args[1]||{},l=function(d,m,g){const b=Ao({span:g}),v=b["sentry-trace"],E=b.baggage;if(!v)return;const _=m.headers||(Xs(d)?d.headers:void 0);if(_){if(function(y){return typeof Headers<"u"&&me(y,Headers)}(_)){const y=new Headers(_);if(y.get("sentry-trace")||y.set("sentry-trace",v),E){const T=y.get("baggage");T?Dt(T)||y.set("baggage",`${T},${E}`):y.set("baggage",E)}return y}if(Array.isArray(_)){const y=[..._];_.find(x=>x[0]==="sentry-trace")||y.push(["sentry-trace",v]);const T=_.find(x=>x[0]==="baggage"&&Dt(x[1]));return E&&!T&&y.push(["baggage",E]),y}{const y="sentry-trace"in _?_["sentry-trace"]:void 0,T="baggage"in _?_.baggage:void 0,x=T?Array.isArray(T)?[...T]:[T]:[],D=T&&(Array.isArray(T)?T.find(S=>Dt(S)):Dt(T));return E&&!D&&x.push(E),{..._,"sentry-trace":y??v,baggage:x.length>0?x.join(","):void 0}}}return{...b}}(h,f,Ee()&&c?u:void 0);l&&(t.args[1]=f,f.headers=l)}const p=j();if(p){const h={input:t.args,response:t.response,startTimestamp:t.startTimestamp,endTimestamp:t.endTimestamp};p.emit("beforeOutgoingRequestSpan",u,h)}return u}function Dt(t){return t.split(",").some(e=>e.trim().startsWith(pr))}function Sa(t,e,n,r){const s={url:t,type:"fetch","http.method":n,[B]:r,[Ue]:"http.client"};return e&&(Oo(e)||(s["http.url"]=e.href,s["server.address"]=e.host),e.search&&(s["http.query"]=e.search),e.hash&&(s["http.fragment"]=e.hash)),s}function vs(t){return t===void 0?void 0:t>=400&&t<500?"warning":t>=500?"error":void 0}const yt=N;function Do(){if(!("fetch"in yt))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch{return!1}}function Yn(t){return t&&/^function\s+\w+\(\)\s+\{\s+\[native code\]\s+\}$/.test(t.toString())}function Lo(t,e){const n="fetch";Se(n,t),we(n,()=>No(void 0,e))}function No(t,e=!1){e&&!function(){var s;if(typeof EdgeRuntime=="string")return!0;if(!Do())return!1;if(Yn(yt.fetch))return!0;let n=!1;const r=yt.document;if(r&&typeof r.createElement=="function")try{const o=r.createElement("iframe");o.hidden=!0,r.head.appendChild(o),(s=o.contentWindow)!=null&&s.fetch&&(n=Yn(o.contentWindow.fetch)),r.head.removeChild(o)}catch(o){C&&w.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",o)}return n}()||Z(N,"fetch",function(n){return function(...r){const s=new Error,{method:o,url:i}=function(c){if(c.length===0)return{method:"GET",url:""};if(c.length===2){const[p,h]=c;return{url:ys(p),method:Kn(h,"method")?String(h.method).toUpperCase():"GET"}}const u=c[0];return{url:ys(u),method:Kn(u,"method")?String(u.method).toUpperCase():"GET"}}(r),a={args:r,fetchData:{method:o,url:i},startTimestamp:1e3*Y(),virtualError:s,headers:Ea(r)};return t||re("fetch",{...a}),n.apply(N,r).then(async c=>(t?t(c):re("fetch",{...a,endTimestamp:1e3*Y(),response:c}),c),c=>{if(re("fetch",{...a,endTimestamp:1e3*Y(),error:c}),ar(c)&&c.stack===void 0&&(c.stack=s.stack,Q(c,"framesToPop",1)),c instanceof TypeError&&(c.message==="Failed to fetch"||c.message==="Load failed"||c.message==="NetworkError when attempting to fetch resource."))try{const u=new URL(a.fetchData.url);c.message=`${c.message} (${u.host})`}catch{}throw c})}})}function wa(t){let e;try{e=t.clone()}catch{return}(async function(n,r){if(n!=null&&n.body){const s=n.body,o=s.getReader(),i=setTimeout(()=>{s.cancel().then(null,()=>{})},9e4);let a=!0;for(;a;){let c;try{c=setTimeout(()=>{s.cancel().then(null,()=>{})},5e3);const{done:u}=await o.read();clearTimeout(c),u&&(r(),a=!1)}catch{a=!1}finally{clearTimeout(c)}}clearTimeout(i),o.releaseLock(),s.cancel().then(null,()=>{})}})(e,()=>{re("fetch-body-resolved",{endTimestamp:1e3*Y(),response:t})})}function Kn(t,e){return!!t&&typeof t=="object"&&!!t[e]}function ys(t){return typeof t=="string"?t:t?Kn(t,"url")?t.url:t.toString?t.toString():"":""}function Ea(t){const[e,n]=t;try{if(typeof n=="object"&&n!==null&&"headers"in n&&n.headers)return new Headers(n.headers);if(Xs(e))return new Headers(e.headers)}catch{}}const L=N;let Xn=0;function bs(){return Xn>0}function Qe(t,e={}){if(!function(r){return typeof r=="function"}(t))return t;try{const r=t.__sentry_wrapped__;if(r)return typeof r=="function"?r:t;if(dr(t))return t}catch{return t}const n=function(...r){try{const s=r.map(o=>Qe(o,e));return t.apply(this,s)}catch(s){throw Xn++,setTimeout(()=>{Xn--}),mt(o=>{o.addEventProcessor(i=>(e.mechanism&&(jn(i,void 0),Ye(i,e.mechanism)),i.extra={...i.extra,arguments:r},i)),Gn(s)}),s}};try{for(const r in t)Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r])}catch{}Qs(n,t),Q(t,"__sentry_wrapped__",n);try{Object.getOwnPropertyDescriptor(n,"name").configurable&&Object.defineProperty(n,"name",{get:()=>t.name})}catch{}return n}function Zn(){const t=wt(),{referrer:e}=L.document||{},{userAgent:n}=L.navigator||{};return{url:t,headers:{...e&&{Referer:e},...n&&{"User-Agent":n}}}}function mr(t,e){const n=gr(t,e),r={type:ka(e),value:Ca(e)};return n.length&&(r.stacktrace={frames:n}),r.type===void 0&&r.value===""&&(r.value="Unrecoverable error caught"),r}function xa(t,e,n,r){const s=j(),o=s==null?void 0:s.getOptions().normalizeDepth,i=function(u){for(const p in u)if(Object.prototype.hasOwnProperty.call(u,p)){const h=u[p];if(h instanceof Error)return h}}(e),a={__serialized__:yo(e,o)};if(i)return{exception:{values:[mr(t,i)]},extra:a};const c={exception:{values:[{type:tn(e)?e.constructor.name:r?"UnhandledRejection":"Error",value:Ia(e,{isUnhandledRejection:r})}]},extra:a};if(n){const u=gr(t,n);u.length&&(c.exception.values[0].stacktrace={frames:u})}return c}function yn(t,e){return{exception:{values:[mr(t,e)]}}}function gr(t,e){const n=e.stacktrace||e.stack||"",r=function(o){return o&&Ta.test(o.message)?1:0}(e),s=function(o){return typeof o.framesToPop=="number"?o.framesToPop:0}(e);try{return t(n,r,s)}catch{}return[]}const Ta=/Minified React error #\d+;/i;function Mo(t){return typeof WebAssembly<"u"&&WebAssembly.Exception!==void 0&&t instanceof WebAssembly.Exception}function ka(t){const e=t==null?void 0:t.name;return!e&&Mo(t)?t.message&&Array.isArray(t.message)&&t.message.length==2?t.message[0]:"WebAssembly.Exception":e}function Ca(t){const e=t==null?void 0:t.message;return Mo(t)?Array.isArray(t.message)&&t.message.length==2?t.message[1]:"wasm exception":e?e.error&&typeof e.error.message=="string"?e.error.message:e:"No error message"}function Qn(t,e,n,r,s){let o;if(Ys(e)&&e.error)return yn(t,e.error);if(xr(e)||nt(e,"DOMException")){const i=e;if("stack"in e)o=yn(t,e);else{const a=i.name||(xr(i)?"DOMError":"DOMException"),c=i.message?`${a}: ${i.message}`:a;o=er(t,c,n,r),jn(o,c)}return"code"in i&&(o.tags={...o.tags,"DOMException.code":`${i.code}`}),o}return ar(e)?yn(t,e):ft(e)||tn(e)?(o=xa(t,e,n,s),Ye(o,{synthetic:!0}),o):(o=er(t,e,n,r),jn(o,`${e}`),Ye(o,{synthetic:!0}),o)}function er(t,e,n,r){const s={};if(r&&n){const o=gr(t,n);o.length&&(s.exception={values:[{value:e,stacktrace:{frames:o}}]}),Ye(s,{synthetic:!0})}if(cr(e)){const{__sentry_template_string__:o,__sentry_template_values__:i}=e;return s.logentry={message:o,params:i},s}return s.message=e,s}function Ia(t,{isUnhandledRejection:e}){const n=function(s,o=40){const i=Object.keys(eo(s));i.sort();const a=i[0];if(!a)return"[object has no keys]";if(a.length>=o)return Gt(a,o);for(let c=i.length;c>0;c--){const u=i.slice(0,c).join(", ");if(!(u.length>o))return c===i.length?u:Gt(u,o)}return""}(t),r=e?"promise rejection":"exception";return Ys(t)?`Event \`ErrorEvent\` captured as ${r} with message \`${t.message}\``:tn(t)?`Event \`${function(s){try{const o=Object.getPrototypeOf(s);return o?o.constructor.name:void 0}catch{}}(t)}\` (type=${t.type}) captured as ${r}`:`Object captured as ${r} with keys: ${n}`}class $a extends ea{constructor(e){const n={parentSpanIsAlwaysRootSpan:!0,...e};Po(n,"browser",["browser"],L.SENTRY_SDK_SOURCE||"npm"),super(n);const r=this,{sendDefaultPii:s,_experiments:o}=r._options,i=o==null?void 0:o.enableLogs;n.sendClientReports&&L.document&&L.document.addEventListener("visibilitychange",()=>{L.document.visibilityState==="hidden"&&(this._flushOutcomes(),i&&vn(r))}),i&&(r.on("flush",()=>{vn(r)}),r.on("afterCaptureLog",()=>{r._logFlushIdleTimeout&&clearTimeout(r._logFlushIdleTimeout),r._logFlushIdleTimeout=setTimeout(()=>{vn(r)},5e3)})),s&&(r.on("postprocessEvent",aa),r.on("beforeSendSession",ca))}eventFromException(e,n){return function(r,s,o,i){const a=Qn(r,s,(o==null?void 0:o.syntheticException)||void 0,i);return Ye(a),a.level="error",o!=null&&o.event_id&&(a.event_id=o.event_id),He(a)}(this._options.stackParser,e,n,this._options.attachStacktrace)}eventFromMessage(e,n="info",r){return function(s,o,i="info",a,c){const u=er(s,o,(a==null?void 0:a.syntheticException)||void 0,c);return u.level=i,a!=null&&a.event_id&&(u.event_id=a.event_id),He(u)}(this._options.stackParser,e,n,r,this._options.attachStacktrace)}_prepareEvent(e,n,r,s){return e.platform=e.platform||"javascript",super._prepareEvent(e,n,r,s)}}const _r=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,et=(t,e,n,r)=>{let s,o;return i=>{e.value>=0&&(i||r)&&(o=e.value-(s||0),(o||s===void 0)&&(s=e.value,e.delta=o,e.rating=((a,c)=>a>c[1]?"poor":a>c[0]?"needs-improvement":"good")(e.value,n),t(e)))}},A=N,bt=(t=!0)=>{var n,r;const e=(r=(n=A.performance)==null?void 0:n.getEntriesByType)==null?void 0:r.call(n,"navigation")[0];if(!t||e&&e.responseStart>0&&e.responseStart<performance.now())return e},Ct=()=>{const t=bt();return(t==null?void 0:t.activationStart)||0},tt=(t,e)=>{var s,o;const n=bt();let r="navigate";return n&&((s=A.document)!=null&&s.prerendering||Ct()>0?r="prerender":(o=A.document)!=null&&o.wasDiscarded?r="restore":n.type&&(r=n.type.replace(/_/g,"-"))),{name:t,value:e===void 0?-1:e,rating:"good",delta:0,entries:[],id:`v4-${Date.now()}-${Math.floor(8999999999999*Math.random())+1e12}`,navigationType:r}},Be=(t,e,n)=>{try{if(PerformanceObserver.supportedEntryTypes.includes(t)){const r=new PerformanceObserver(s=>{Promise.resolve().then(()=>{e(s.getEntries())})});return r.observe(Object.assign({type:t,buffered:!0},n||{})),r}}catch{}},st=t=>{const e=n=>{var r;n.type!=="pagehide"&&((r=A.document)==null?void 0:r.visibilityState)!=="hidden"||t(n)};A.document&&(addEventListener("visibilitychange",e,!0),addEventListener("pagehide",e,!0))},an=t=>{let e=!1;return()=>{e||(t(),e=!0)}};let ct=-1;const Zt=t=>{A.document.visibilityState==="hidden"&&ct>-1&&(ct=t.type==="visibilitychange"?t.timeStamp:0,Pa())},Pa=()=>{removeEventListener("visibilitychange",Zt,!0),removeEventListener("prerenderingchange",Zt,!0)},cn=()=>(A.document&&ct<0&&(ct=A.document.visibilityState!=="hidden"||A.document.prerendering?1/0:0,addEventListener("visibilitychange",Zt,!0),addEventListener("prerenderingchange",Zt,!0)),{get firstHiddenTime(){return ct}}),It=t=>{var e;(e=A.document)!=null&&e.prerendering?addEventListener("prerenderingchange",()=>t(),!0):t()},Aa=[1800,3e3],Oa=[.1,.25],Ra=(t,e={})=>{((n,r={})=>{It(()=>{const s=cn(),o=tt("FCP");let i;const a=Be("paint",c=>{c.forEach(u=>{u.name==="first-contentful-paint"&&(a.disconnect(),u.startTime<s.firstHiddenTime&&(o.value=Math.max(u.startTime-Ct(),0),o.entries.push(u),i(!0)))})});a&&(i=et(n,o,Aa,r.reportAllChanges))})})(an(()=>{const n=tt("CLS",0);let r,s=0,o=[];const i=c=>{c.forEach(u=>{if(!u.hadRecentInput){const p=o[0],h=o[o.length-1];s&&p&&h&&u.startTime-h.startTime<1e3&&u.startTime-p.startTime<5e3?(s+=u.value,o.push(u)):(s=u.value,o=[u])}}),s>n.value&&(n.value=s,n.entries=o,r())},a=Be("layout-shift",i);a&&(r=et(t,n,Oa,e.reportAllChanges),st(()=>{i(a.takeRecords()),r(!0)}),setTimeout(r,0))}))},Da=[100,300],La=(t,e={})=>{It(()=>{const n=cn(),r=tt("FID");let s;const o=c=>{c.startTime<n.firstHiddenTime&&(r.value=c.processingStart-c.startTime,r.entries.push(c),s(!0))},i=c=>{c.forEach(o)},a=Be("first-input",i);s=et(t,r,Da,e.reportAllChanges),a&&st(an(()=>{i(a.takeRecords()),a.disconnect()}))})};let jo=0,bn=1/0,Lt=0;const Na=t=>{t.forEach(e=>{e.interactionId&&(bn=Math.min(bn,e.interactionId),Lt=Math.max(Lt,e.interactionId),jo=Lt?(Lt-bn)/7+1:0)})};let tr;const Ma=()=>{"interactionCount"in performance||tr||(tr=Be("event",Na,{type:"event",buffered:!0,durationThreshold:0}))},ue=[],Sn=new Map,ja=()=>(tr?jo:performance.interactionCount||0)-0,Fa=[],qa=t=>{var r;if(Fa.forEach(s=>s(t)),!t.interactionId&&t.entryType!=="first-input")return;const e=ue[ue.length-1],n=Sn.get(t.interactionId);if(n||ue.length<10||e&&t.duration>e.latency){if(n)t.duration>n.latency?(n.entries=[t],n.latency=t.duration):t.duration===n.latency&&t.startTime===((r=n.entries[0])==null?void 0:r.startTime)&&n.entries.push(t);else{const s={id:t.interactionId,latency:t.duration,entries:[t]};Sn.set(s.id,s),ue.push(s)}ue.sort((s,o)=>o.latency-s.latency),ue.length>10&&ue.splice(10).forEach(s=>Sn.delete(s.id))}},Fo=t=>{var r;const e=A.requestIdleCallback||A.setTimeout;let n=-1;return t=an(t),((r=A.document)==null?void 0:r.visibilityState)==="hidden"?t():(n=e(t),st(t)),n},Ua=[200,500],Ha=(t,e={})=>{"PerformanceEventTiming"in A&&"interactionId"in PerformanceEventTiming.prototype&&It(()=>{Ma();const n=tt("INP");let r;const s=i=>{Fo(()=>{i.forEach(qa);const a=(()=>{const c=Math.min(ue.length-1,Math.floor(ja()/50));return ue[c]})();a&&a.latency!==n.value&&(n.value=a.latency,n.entries=a.entries,r())})},o=Be("event",s,{durationThreshold:e.durationThreshold!=null?e.durationThreshold:40});r=et(t,n,Ua,e.reportAllChanges),o&&(o.observe({type:"first-input",buffered:!0}),st(()=>{s(o.takeRecords()),r(!0)}))})},Ba=[2500,4e3],Ss={},za=(t,e={})=>{It(()=>{const n=cn(),r=tt("LCP");let s;const o=a=>{e.reportAllChanges||(a=a.slice(-1)),a.forEach(c=>{c.startTime<n.firstHiddenTime&&(r.value=Math.max(c.startTime-Ct(),0),r.entries=[c],s())})},i=Be("largest-contentful-paint",o);if(i){s=et(t,r,Ba,e.reportAllChanges);const a=an(()=>{Ss[r.id]||(o(i.takeRecords()),i.disconnect(),Ss[r.id]=!0,s(!0))});["keydown","click"].forEach(c=>{A.document&&addEventListener(c,()=>Fo(a),{once:!0,capture:!0})}),st(a)}})},Wa=[800,1800],nr=t=>{var e,n;(e=A.document)!=null&&e.prerendering?It(()=>nr(t)):((n=A.document)==null?void 0:n.readyState)!=="complete"?addEventListener("load",()=>nr(t),!0):setTimeout(t,0)},Ja=(t,e={})=>{const n=tt("TTFB"),r=et(t,n,Wa,e.reportAllChanges);nr(()=>{const s=bt();s&&(n.value=Math.max(s.responseStart-Ct(),0),n.entries=[s],r(!0))})},ut={},Qt={};let qo,Uo,Ho,Bo,zo;function Wo(t,e=!1){return dt("cls",t,Ga,qo,e)}function at(t,e){return Jo(t,e),Qt[t]||(function(n){const r={};n==="event"&&(r.durationThreshold=0),Be(n,s=>{ot(n,{entries:s})},r)}(t),Qt[t]=!0),Go(t,e)}function ot(t,e){const n=ut[t];if(n!=null&&n.length)for(const r of n)try{r(e)}catch(s){_r&&w.error(`Error while triggering instrumentation handler.
Type: ${t}
Name: ${_e(r)}
Error:`,s)}}function Ga(){return Ra(t=>{ot("cls",{metric:t}),qo=t},{reportAllChanges:!0})}function Va(){return La(t=>{ot("fid",{metric:t}),Uo=t})}function Ya(){return za(t=>{ot("lcp",{metric:t}),Ho=t},{reportAllChanges:!0})}function Ka(){return Ja(t=>{ot("ttfb",{metric:t}),Bo=t})}function Xa(){return Ha(t=>{ot("inp",{metric:t}),zo=t})}function dt(t,e,n,r,s=!1){let o;return Jo(t,e),Qt[t]||(o=n(),Qt[t]=!0),r&&e({metric:r}),Go(t,e,s?o:void 0)}function Jo(t,e){ut[t]=ut[t]||[],ut[t].push(e)}function Go(t,e,n){return()=>{n&&n();const r=ut[t];if(!r)return;const s=r.indexOf(e);s!==-1&&r.splice(s,1)}}function wn(t){return typeof t=="number"&&isFinite(t)}function pe(t,e,n,{...r}){const s=F(t).start_timestamp;return s&&s>e&&typeof t.updateStartTime=="function"&&t.updateStartTime(e),wo(t,()=>{const o=kt({startTime:e,...r});return o&&o.end(n),o})}function Vo(t){var m;const e=j();if(!e)return;const{name:n,transaction:r,attributes:s,startTime:o}=t,{release:i,environment:a,sendDefaultPii:c}=e.getOptions(),u=e.getIntegrationByName("Replay"),p=u==null?void 0:u.getReplayId(),h=q(),f=h.getUser(),l=f!==void 0?f.email||f.id||f.ip_address:void 0;let d;try{d=h.getScopeData().contexts.profile.profile_id}catch{}return kt({name:n,attributes:{release:i,environment:a,user:l||void 0,profile_id:d||void 0,replay_id:p||void 0,transaction:r,"user_agent.original":(m=A.navigator)==null?void 0:m.userAgent,"client.address":c?"{{auto}}":void 0,...s},startTime:o,experimental:{standalone:!0}})}function vr(){return A.addEventListener&&A.performance}function U(t){return t/1e3}function Yo(t){let e="unknown",n="unknown",r="";for(const s of t){if(s==="/"){[e,n]=t.split("/");break}if(!isNaN(Number(s))){e=r==="h"?"http":r,n=t.split(r)[1];break}r+=s}return r===t&&(e=r),{name:e,version:n}}function Za(){let t,e,n=0;if(!function(){try{return PerformanceObserver.supportedEntryTypes.includes("layout-shift")}catch{return!1}}())return;let r=!1;function s(){r||(r=!0,e&&function(i,a,c){var d;_r&&w.log(`Sending CLS span (${i})`);const u=U((te()||0)+((a==null?void 0:a.startTime)||0)),p=q().getScopeData().transactionName,h=a?qe((d=a.sources[0])==null?void 0:d.node):"Layout shift",f={[B]:"auto.http.browser.cls",[Ue]:"ui.webvital.cls",[gt]:(a==null?void 0:a.duration)||0,"sentry.pageload.span_id":c},l=Vo({name:h,transaction:p,attributes:f,startTime:u});l&&(l.addEvent("cls",{[rn]:"",[sn]:i}),l.end(u))}(n,t,e),o())}const o=Wo(({metric:i})=>{const a=i.entries[i.entries.length-1];a&&(n=i.value,t=a)},!0);st(()=>{s()}),setTimeout(()=>{const i=j();if(!i)return;const a=i.on("startNavigationSpan",()=>{s(),a==null||a()}),c=X();if(c){const u=V(c);F(u).op==="pageload"&&(e=u.spanContext().spanId)}},0)}const Qa=2147483647;let K,Je,ws=0,W={};function ec({recordClsStandaloneSpans:t}){const e=vr();if(e&&te()){e.mark&&A.performance.mark("sentry-tracing-init");const n=dt("fid",({metric:i})=>{const a=i.entries[i.entries.length-1];if(!a)return;const c=U(te()),u=U(a.startTime);W.fid={value:i.value,unit:"millisecond"},W["mark.fid"]={value:c+u,unit:"second"}},Va,Uo),r=function(i,a=!1){return dt("lcp",i,Ya,Ho,a)}(({metric:i})=>{const a=i.entries[i.entries.length-1];a&&(W.lcp={value:i.value,unit:"millisecond"},K=a)},!0),s=function(i){return dt("ttfb",i,Ka,Bo)}(({metric:i})=>{i.entries[i.entries.length-1]&&(W.ttfb={value:i.value,unit:"millisecond"})}),o=t?Za():Wo(({metric:i})=>{const a=i.entries[i.entries.length-1];a&&(W.cls={value:i.value,unit:""},Je=a)},!0);return()=>{n(),r(),s(),o==null||o()}}return()=>{}}function tc(t,e){const n=vr(),r=te();if(!(n!=null&&n.getEntries)||!r)return;const s=U(r),o=n.getEntries(),{op:i,start_timestamp:a}=F(t);if(o.slice(ws).forEach(c=>{const u=U(c.startTime),p=U(Math.max(0,c.duration));if(!(i==="navigation"&&a&&s+u<a))switch(c.entryType){case"navigation":(function(h,f,l){["unloadEvent","redirect","domContentLoadedEvent","loadEvent","connect"].forEach(d=>{Nt(h,f,d,l)}),Nt(h,f,"secureConnection",l,"TLS/SSL"),Nt(h,f,"fetch",l,"cache"),Nt(h,f,"domainLookup",l,"DNS"),function(d,m,g){const b=g+U(m.requestStart),v=g+U(m.responseEnd),E=g+U(m.responseStart);m.responseEnd&&(pe(d,b,v,{op:"browser.request",name:m.name,attributes:{[B]:"auto.ui.browser.metrics"}}),pe(d,E,v,{op:"browser.response",name:m.name,attributes:{[B]:"auto.ui.browser.metrics"}}))}(h,f,l)})(t,c,s);break;case"mark":case"paint":case"measure":{(function(l,d,m,g,b){const v=bt(!1),E=U(v?v.requestStart:0),_=b+Math.max(m,E),y=b+m,T=y+g,x={[B]:"auto.resource.browser.metrics"};if(_!==y&&(x["sentry.browser.measure_happened_before_request"]=!0,x["sentry.browser.measure_start_time"]=_),d.detail)if(typeof d.detail=="object")for(const[D,S]of Object.entries(d.detail))if(S&&pt(S))x[`sentry.browser.measure.detail.${D}`]=S;else try{x[`sentry.browser.measure.detail.${D}`]=JSON.stringify(S)}catch{}else if(pt(d.detail))x["sentry.browser.measure.detail"]=d.detail;else try{x["sentry.browser.measure.detail"]=JSON.stringify(d.detail)}catch{}_<=T&&pe(l,_,T,{name:d.name,op:d.entryType,attributes:x})})(t,c,u,p,s);const h=cn(),f=c.startTime<h.firstHiddenTime;c.name==="first-paint"&&f&&(W.fp={value:c.startTime,unit:"millisecond"}),c.name==="first-contentful-paint"&&f&&(W.fcp={value:c.startTime,unit:"millisecond"});break}case"resource":(function(h,f,l,d,m,g){if(f.initiatorType==="xmlhttprequest"||f.initiatorType==="fetch")return;const b=Ve(l),v={[B]:"auto.resource.browser.metrics"};En(v,f,"transferSize","http.response_transfer_size"),En(v,f,"encodedBodySize","http.response_content_length"),En(v,f,"decodedBodySize","http.decoded_response_content_length");const E=f.deliveryType;E!=null&&(v["http.response_delivery_type"]=E);const _=f.renderBlockingStatus;_&&(v["resource.render_blocking_status"]=_),b.protocol&&(v["url.scheme"]=b.protocol.split(":").pop()),b.host&&(v["server.address"]=b.host),v["url.same_origin"]=l.includes(A.location.origin);const{name:y,version:T}=Yo(f.nextHopProtocol);v["network.protocol.name"]=y,v["network.protocol.version"]=T;const x=g+d,D=x+m;pe(h,x,D,{name:l.replace(A.location.origin,""),op:f.initiatorType?`resource.${f.initiatorType}`:"resource.other",attributes:v})})(t,c,c.name,u,p,s)}}),ws=Math.max(o.length-1,0),function(c){const u=A.navigator;if(!u)return;const p=u.connection;p&&(p.effectiveType&&c.setAttribute("effectiveConnectionType",p.effectiveType),p.type&&c.setAttribute("connectionType",p.type),wn(p.rtt)&&(W["connection.rtt"]={value:p.rtt,unit:"millisecond"})),wn(u.deviceMemory)&&c.setAttribute("deviceMemory",`${u.deviceMemory} GB`),wn(u.hardwareConcurrency)&&c.setAttribute("hardwareConcurrency",String(u.hardwareConcurrency))}(t),i==="pageload"){(function(u){const p=bt(!1);if(!p)return;const{responseStart:h,requestStart:f}=p;f<=h&&(u["ttfb.requestTime"]={value:h-f,unit:"millisecond"})})(W);const c=W["mark.fid"];c&&W.fid&&(pe(t,c.value,c.value+U(W.fid.value),{name:"first input delay",op:"ui.action",attributes:{[B]:"auto.ui.browser.metrics"}}),delete W["mark.fid"]),"fcp"in W&&e.recordClsOnPageloadSpan||delete W.cls,Object.entries(W).forEach(([u,p])=>{(function(h,f,l,d=X()){const m=d&&V(d);m&&(C&&w.log(`[Measurement] Setting measurement on root span: ${h} = ${f} ${l}`),m.addEvent(h,{[sn]:f,[rn]:l}))})(u,p.value,p.unit)}),t.setAttribute("performance.timeOrigin",s),t.setAttribute("performance.activationStart",Ct()),function(u){K&&(K.element&&u.setAttribute("lcp.element",qe(K.element)),K.id&&u.setAttribute("lcp.id",K.id),K.url&&u.setAttribute("lcp.url",K.url.trim().slice(0,200)),K.loadTime!=null&&u.setAttribute("lcp.loadTime",K.loadTime),K.renderTime!=null&&u.setAttribute("lcp.renderTime",K.renderTime),u.setAttribute("lcp.size",K.size)),Je!=null&&Je.sources&&Je.sources.forEach((p,h)=>u.setAttribute(`cls.source.${h+1}`,qe(p.node)))}(t)}K=void 0,Je=void 0,W={}}function Nt(t,e,n,r,s=n){const o=function(c){return c==="secureConnection"?"connectEnd":c==="fetch"?"domainLookupStart":`${c}End`}(n),i=e[o],a=e[`${n}Start`];a&&i&&pe(t,r+U(a),r+U(i),{op:`browser.${s}`,name:e.name,attributes:{[B]:"auto.ui.browser.metrics",...n==="redirect"&&e.redirectCount!=null?{"http.redirect_count":e.redirectCount}:{}}})}function En(t,e,n,r){const s=e[n];s!=null&&s<Qa&&(t[r]=s)}const nc=1e3;let Es,xn,Tn,Mt;function rc(){if(!A.document)return;const t=re.bind(null,"dom"),e=xs(t,!0);A.document.addEventListener("click",e,!1),A.document.addEventListener("keypress",e,!1),["EventTarget","Node"].forEach(n=>{var o,i;const r=A,s=(o=r[n])==null?void 0:o.prototype;(i=s==null?void 0:s.hasOwnProperty)!=null&&i.call(s,"addEventListener")&&(Z(s,"addEventListener",function(a){return function(c,u,p){if(c==="click"||c=="keypress")try{const h=this.__sentry_instrumentation_handlers__=this.__sentry_instrumentation_handlers__||{},f=h[c]=h[c]||{refCount:0};if(!f.handler){const l=xs(t);f.handler=l,a.call(this,c,l,p)}f.refCount++}catch{}return a.call(this,c,u,p)}}),Z(s,"removeEventListener",function(a){return function(c,u,p){if(c==="click"||c=="keypress")try{const h=this.__sentry_instrumentation_handlers__||{},f=h[c];f&&(f.refCount--,f.refCount<=0&&(a.call(this,c,f.handler,p),f.handler=void 0,delete h[c]),Object.keys(h).length===0&&delete this.__sentry_instrumentation_handlers__)}catch{}return a.call(this,c,u,p)}}))})}function xs(t,e=!1){return n=>{if(!n||n._sentryCaptured)return;const r=function(o){try{return o.target}catch{return null}}(n);if(function(o,i){return o==="keypress"&&(!(i!=null&&i.tagName)||i.tagName!=="INPUT"&&i.tagName!=="TEXTAREA"&&!i.isContentEditable)}(n.type,r))return;Q(n,"_sentryCaptured",!0),r&&!r._sentryId&&Q(r,"_sentryId",ne());const s=n.type==="keypress"?"input":n.type;(function(o){if(o.type!==xn)return!1;try{if(!o.target||o.target._sentryId!==Tn)return!1}catch{}return!0})(n)||(t({event:n,name:s,global:e}),xn=n.type,Tn=r?r._sentryId:void 0),clearTimeout(Es),Es=A.setTimeout(()=>{Tn=void 0,xn=void 0},nc)}}function yr(t){const e="history";Se(e,t),we(e,sc)}function sc(){function t(e){return function(...n){const r=n.length>2?n[2]:void 0;if(r){const s=Mt,o=function(i){try{return new URL(i,A.location.origin).toString()}catch{return i}}(String(r));if(Mt=o,s===o)return e.apply(this,n);re("history",{from:s,to:o})}return e.apply(this,n)}}A.addEventListener("popstate",()=>{const e=A.location.href,n=Mt;Mt=e,n!==e&&re("history",{from:n,to:e})}),"history"in yt&&yt.history&&(Z(A.history,"pushState",t),Z(A.history,"replaceState",t))}const Bt={};function Ts(t){Bt[t]=void 0}const Ge="__sentry_xhr_v3__";function Ko(t){Se("xhr",t),we("xhr",oc)}function oc(){if(!A.XMLHttpRequest)return;const t=XMLHttpRequest.prototype;t.open=new Proxy(t.open,{apply(e,n,r){const s=new Error,o=1e3*Y(),i=fe(r[0])?r[0].toUpperCase():void 0,a=function(u){if(fe(u))return u;try{return u.toString()}catch{}}(r[1]);if(!i||!a)return e.apply(n,r);n[Ge]={method:i,url:a,request_headers:{}},i==="POST"&&a.match(/sentry_key/)&&(n.__sentry_own_request__=!0);const c=()=>{const u=n[Ge];if(u&&n.readyState===4){try{u.status_code=n.status}catch{}re("xhr",{endTimestamp:1e3*Y(),startTimestamp:o,xhr:n,virtualError:s})}};return"onreadystatechange"in n&&typeof n.onreadystatechange=="function"?n.onreadystatechange=new Proxy(n.onreadystatechange,{apply:(u,p,h)=>(c(),u.apply(p,h))}):n.addEventListener("readystatechange",c),n.setRequestHeader=new Proxy(n.setRequestHeader,{apply(u,p,h){const[f,l]=h,d=p[Ge];return d&&fe(f)&&fe(l)&&(d.request_headers[f.toLowerCase()]=l),u.apply(p,h)}}),e.apply(n,r)}}),t.send=new Proxy(t.send,{apply(e,n,r){const s=n[Ge];return s?(r[0]!==void 0&&(s.body=r[0]),re("xhr",{startTimestamp:1e3*Y(),xhr:n}),e.apply(n,r)):e.apply(n,r)}})}const kn=[],zt=new Map;function ic(){if(vr()&&te()){const t=dt("inp",({metric:e})=>{if(e.value==null)return;const n=e.entries.find(f=>f.duration===e.value&&ks[f.name]);if(!n)return;const{interactionId:r}=n,s=ks[n.name],o=U(te()+n.startTime),i=U(e.value),a=X(),c=a?V(a):void 0,u=(r!=null?zt.get(r):void 0)||c,p=u?F(u).description:q().getScopeData().transactionName,h=Vo({name:qe(n.target),transaction:p,attributes:{[B]:"auto.http.browser.inp",[Ue]:`ui.interaction.${s}`,[gt]:n.duration},startTime:o});h&&(h.addEvent("inp",{[rn]:"millisecond",[sn]:e.value}),h.end(o+i))},Xa,zo);return()=>{t()}}return()=>{}}const ks={click:"click",pointerdown:"click",pointerup:"click",mousedown:"click",mouseup:"click",touchstart:"click",touchend:"click",mouseover:"hover",mouseout:"hover",mouseenter:"hover",mouseleave:"hover",pointerover:"hover",pointerout:"hover",pointerenter:"hover",pointerleave:"hover",dragstart:"drag",dragend:"drag",drag:"drag",dragenter:"drag",dragleave:"drag",dragover:"drag",drop:"drag",keydown:"press",keyup:"press",keypress:"press",input:"press"};function ac(t,e=function(n){const r=Bt[n];if(r)return r;let s=A[n];if(Yn(s))return Bt[n]=s.bind(A);const o=A.document;if(o&&typeof o.createElement=="function")try{const i=o.createElement("iframe");i.hidden=!0,o.head.appendChild(i);const a=i.contentWindow;a!=null&&a[n]&&(s=a[n]),o.head.removeChild(i)}catch(i){_r&&w.warn(`Could not create sandbox iframe for ${n} check, bailing to window.${n}: `,i)}return s&&(Bt[n]=s.bind(A))}("fetch")){let n=0,r=0;return ia(t,function(s){const o=s.body.length;n+=o,r++;const i={body:s.body,method:"POST",referrerPolicy:"strict-origin",headers:t.headers,keepalive:n<=6e4&&r<15,...t.fetchOptions};if(!e)return Ts("fetch"),Xt("No fetch implementation available");try{return e(t.url,i).then(a=>(n-=o,r--,{statusCode:a.status,headers:{"x-sentry-rate-limits":a.headers.get("X-Sentry-Rate-Limits"),"retry-after":a.headers.get("Retry-After")}}))}catch(a){return Ts("fetch"),n-=o,r--,Xt(a)}})}function Cn(t,e,n,r){const s={filename:t,function:e==="<anonymous>"?je:e,in_app:!0};return n!==void 0&&(s.lineno=n),r!==void 0&&(s.colno=r),s}const cc=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,uc=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,dc=/\((\S*)(?::(\d+))(?::(\d+))\)/,lc=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,pc=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,fc=fo([30,t=>{const e=cc.exec(t);if(e){const[,r,s,o]=e;return Cn(r,je,+s,+o)}const n=uc.exec(t);if(n){if(n[2]&&n[2].indexOf("eval")===0){const o=dc.exec(n[2]);o&&(n[2]=o[1],n[3]=o[2],n[4]=o[3])}const[r,s]=Cs(n[1]||je,n[2]);return Cn(s,r,n[3]?+n[3]:void 0,n[4]?+n[4]:void 0)}}],[50,t=>{const e=lc.exec(t);if(e){if(e[3]&&e[3].indexOf(" > eval")>-1){const s=pc.exec(e[3]);s&&(e[1]=e[1]||"eval",e[3]=s[1],e[4]=s[2],e[5]="")}let n=e[3],r=e[1]||je;return[r,n]=Cs(r,n),Cn(n,r,e[4]?+e[4]:void 0,e[5]?+e[5]:void 0)}}]),Cs=(t,e)=>{const n=t.indexOf("safari-extension")!==-1,r=t.indexOf("safari-web-extension")!==-1;return n||r?[t.indexOf("@")!==-1?t.split("@")[0]:je,n?`safari-extension:${e}`:`safari-web-extension:${e}`]:[t,e]},ee=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,Is=1024,hc=(t={})=>{const e={console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0,...t};return{name:"Breadcrumbs",setup(n){var r;e.console&&function(s){const o="console";Se(o,s),we(o,ma)}(function(s){return function(o){if(j()!==s)return;const i={category:"console",data:{arguments:o.args,logger:"console"},level:ga(o.level),message:Tr(o.args," ")};if(o.level==="assert"){if(o.args[0]!==!1)return;i.message=`Assertion failed: ${Tr(o.args.slice(1)," ")||"console.assert"}`,i.data.arguments=o.args.slice(1)}Ie(i,{input:o.args,level:o.level})}}(n)),e.dom&&(r=function(s,o){return function(i){if(j()!==s)return;let a,c,u=typeof o=="object"?o.serializeAttribute:void 0,p=typeof o=="object"&&typeof o.maxStringLength=="number"?o.maxStringLength:void 0;p&&p>Is&&(ee&&w.warn(`\`dom.maxStringLength\` cannot exceed 1024, but a value of ${p} was configured. Sentry will use 1024 instead.`),p=Is),typeof u=="string"&&(u=[u]);try{const f=i.event,l=function(d){return!!d&&!!d.target}(f)?f.target:f;a=qe(l,{keyAttrs:u,maxStringLength:p}),c=Zs(l)}catch{a="<unknown>"}if(a.length===0)return;const h={category:`ui.${i.name}`,message:a};c&&(h.data={"ui.component_name":c}),Ie(h,{event:i.event,name:i.name,global:i.global})}}(n,e.dom),Se("dom",r),we("dom",rc)),e.xhr&&Ko(function(s){return function(o){if(j()!==s)return;const{startTimestamp:i,endTimestamp:a}=o,c=o.xhr[Ge];if(!i||!a||!c)return;const{method:u,url:p,status_code:h,body:f}=c,l={method:u,url:p,status_code:h},d={xhr:o.xhr,input:f,startTimestamp:i,endTimestamp:a},m={category:"xhr",data:l,type:"http",level:vs(h)};s.emit("beforeOutgoingRequestBreadcrumb",m,d),Ie(m,d)}}(n)),e.fetch&&Lo(function(s){return function(o){if(j()!==s)return;const{startTimestamp:i,endTimestamp:a}=o;if(a&&(!o.fetchData.url.match(/sentry_key/)||o.fetchData.method!=="POST"))if(o.fetchData.method,o.fetchData.url,o.error){const c=o.fetchData,u={data:o.error,input:o.args,startTimestamp:i,endTimestamp:a},p={category:"fetch",data:c,level:"error",type:"http"};s.emit("beforeOutgoingRequestBreadcrumb",p,u),Ie(p,u)}else{const c=o.response,u={...o.fetchData,status_code:c==null?void 0:c.status};o.fetchData.request_body_size,o.fetchData.response_body_size;const p={input:o.args,response:c,startTimestamp:i,endTimestamp:a},h={category:"fetch",data:u,type:"http",level:vs(u.status_code)};s.emit("beforeOutgoingRequestBreadcrumb",h,p),Ie(h,p)}}}(n)),e.history&&yr(function(s){return function(o){if(j()!==s)return;let i=o.from,a=o.to;const c=Ve(L.location.href);let u=i?Ve(i):void 0;const p=Ve(a);u!=null&&u.path||(u=c),c.protocol===p.protocol&&c.host===p.host&&(a=p.relative),c.protocol===u.protocol&&c.host===u.host&&(i=u.relative),Ie({category:"navigation",data:{from:i,to:a}})}}(n)),e.sentry&&n.on("beforeSendEvent",function(s){return function(o){j()===s&&Ie({category:"sentry."+(o.type==="transaction"?"transaction":"event"),event_id:o.event_id,level:o.level,message:Pe(o)},{event:o})}}(n))}}},mc=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","BroadcastChannel","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","SharedWorker","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],gc=(t={})=>{const e={XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0,...t};return{name:"BrowserApiErrors",setupOnce(){e.setTimeout&&Z(L,"setTimeout",$s),e.setInterval&&Z(L,"setInterval",$s),e.requestAnimationFrame&&Z(L,"requestAnimationFrame",_c),e.XMLHttpRequest&&"XMLHttpRequest"in L&&Z(XMLHttpRequest.prototype,"send",vc);const n=e.eventTarget;n&&(Array.isArray(n)?n:mc).forEach(yc)}}};function $s(t){return function(...e){const n=e[0];return e[0]=Qe(n,{mechanism:{data:{function:_e(t)},handled:!1,type:"instrument"}}),t.apply(this,e)}}function _c(t){return function(e){return t.apply(this,[Qe(e,{mechanism:{data:{function:"requestAnimationFrame",handler:_e(t)},handled:!1,type:"instrument"}})])}}function vc(t){return function(...e){const n=this;return["onload","onerror","onprogress","onreadystatechange"].forEach(r=>{r in n&&typeof n[r]=="function"&&Z(n,r,function(s){const o={mechanism:{data:{function:r,handler:_e(s)},handled:!1,type:"instrument"}},i=dr(s);return i&&(o.mechanism.data.handler=_e(i)),Qe(s,o)})}),t.apply(this,e)}}function yc(t){var r,s;const e=L,n=(r=e[t])==null?void 0:r.prototype;(s=n==null?void 0:n.hasOwnProperty)!=null&&s.call(n,"addEventListener")&&(Z(n,"addEventListener",function(o){return function(i,a,c){try{typeof a.handleEvent=="function"&&(a.handleEvent=Qe(a.handleEvent,{mechanism:{data:{function:"handleEvent",handler:_e(a),target:t},handled:!1,type:"instrument"}}))}catch{}return o.apply(this,[i,Qe(a,{mechanism:{data:{function:"addEventListener",handler:_e(a),target:t},handled:!1,type:"instrument"}}),c])}}),Z(n,"removeEventListener",function(o){return function(i,a,c){try{const u=a.__sentry_wrapped__;u&&o.call(this,i,u,c)}catch{}return o.call(this,i,a,c)}}))}const bc=()=>({name:"BrowserSession",setupOnce(){L.document!==void 0?(es({ignoreDuration:!0}),ts(),yr(({from:t,to:e})=>{t!==void 0&&t!==e&&(es({ignoreDuration:!0}),ts())})):ee&&w.warn("Using the `browserSessionIntegration` in non-browser environments is not supported.")}}),Sc=(t={})=>{const e={onerror:!0,onunhandledrejection:!0,...t};return{name:"GlobalHandlers",setupOnce(){Error.stackTraceLimit=50},setup(n){e.onerror&&(function(r){ho(s=>{const{stackParser:o,attachStacktrace:i}=As();if(j()!==r||bs())return;const{msg:a,url:c,line:u,column:p,error:h}=s,f=function(l,d,m,g){const b=l.exception=l.exception||{},v=b.values=b.values||[],E=v[0]=v[0]||{},_=E.stacktrace=E.stacktrace||{},y=_.frames=_.frames||[],T=g,x=m,D=fe(d)&&d.length>0?d:wt();return y.length===0&&y.push({colno:T,filename:D,function:je,in_app:!0,lineno:x}),l}(Qn(o,h||a,void 0,i,!1),c,u,p);f.level="error",Qr(f,{originalException:h,mechanism:{handled:!1,type:"onerror"}})})}(n),Ps("onerror")),e.onunhandledrejection&&(function(r){mo(s=>{const{stackParser:o,attachStacktrace:i}=As();if(j()!==r||bs())return;const a=function(u){if(pt(u))return u;try{if("reason"in u)return u.reason;if("detail"in u&&"reason"in u.detail)return u.detail.reason}catch{}return u}(s),c=pt(a)?{exception:{values:[{type:"UnhandledRejection",value:`Non-Error promise rejection captured with value: ${String(a)}`}]}}:Qn(o,a,void 0,i,!0);c.level="error",Qr(c,{originalException:a,mechanism:{handled:!1,type:"onunhandledrejection"}})})}(n),Ps("onunhandledrejection"))}}};function Ps(t){ee&&w.log(`Global Handler attached: ${t}`)}function As(){const t=j();return(t==null?void 0:t.getOptions())||{stackParser:()=>[],attachStacktrace:!1}}const wc=()=>({name:"HttpContext",preprocessEvent(t){var r;if(!L.navigator&&!L.location&&!L.document)return;const e=Zn(),n={...e.headers,...(r=t.request)==null?void 0:r.headers};t.request={...e,...t.request,headers:n}}}),Ec=(t={})=>{const e=t.limit||5,n=t.key||"cause";return{name:"LinkedErrors",preprocessEvent(r,s,o){ha(mr,o.getOptions().stackParser,n,e,r,s)}}};function xc(t){const e={};for(const n of Object.getOwnPropertyNames(t)){const r=n;t[r]!==void 0&&(e[r]=t[r])}return e}function Tc(t={}){const e=function(s={}){var o;return{defaultIntegrations:[fa(),da(),gc(),hc(),Sc(),Ec(),_a(),wc(),bc()],release:typeof __SENTRY_RELEASE__=="string"?__SENTRY_RELEASE__:(o=L.SENTRY_RELEASE)==null?void 0:o.id,sendClientReports:!0,...xc(s)}}(t);if(!e.skipBrowserExtensionCheck&&function(){var p;const s=L.window!==void 0&&L;if(!s)return!1;const o=s[s.chrome?"chrome":"browser"],i=(p=o==null?void 0:o.runtime)==null?void 0:p.id,a=wt()||"",c=!!i&&L===L.top&&["chrome-extension:","moz-extension:","ms-browser-extension:","safari-web-extension:"].some(h=>a.startsWith(`${h}//`)),u=s.nw!==void 0;return!!i&&!c&&!u}())return void(ee&&We(()=>{console.error("[Sentry] You cannot run Sentry this way in a browser extension, check: https://docs.sentry.io/platforms/javascript/best-practices/browser-extensions/")}));ee&&!Do()&&w.warn("No Fetch API detected. The Sentry SDK requires a Fetch API compatible environment to send events. Please add a Fetch API polyfill.");const n={...e,stackParser:(r=e.stackParser||fc,Array.isArray(r)?fo(...r):r),integrations:Qi(e),transport:e.transport||ac};var r;return ta($a,n)}const Os=new WeakMap,In=new Map,Xo={traceFetch:!0,traceXHR:!0,enableHTTPTimings:!0,trackFetchStreamPerformance:!1};function kc(t,e){const{traceFetch:n,traceXHR:r,trackFetchStreamPerformance:s,shouldCreateSpanForRequest:o,enableHTTPTimings:i,tracePropagationTargets:a,onRequestSpanStart:c}={...Xo,...e},u=typeof o=="function"?o:f=>!0,p=f=>function(l,d){const m=wt();if(m){let g,b;try{g=new URL(l,m),b=new URL(m).origin}catch{return!1}const v=g.origin===b;return d?Oe(g.toString(),d)||v&&Oe(g.pathname,d):v}{const g=!!l.match(/^\/(?!\/)/);return d?Oe(l,d):g}}(f,a),h={};n&&(t.addEventProcessor(f=>(f.type==="transaction"&&f.spans&&f.spans.forEach(l=>{if(l.op==="http.client"){const d=In.get(l.span_id);d&&(l.timestamp=d/1e3,In.delete(l.span_id))}}),f)),s&&function(f){const l="fetch-body-resolved";Se(l,f),we(l,()=>No(wa))}(f=>{if(f.response){const l=Os.get(f.response);l&&f.endTimestamp&&In.set(l,f.endTimestamp)}}),Lo(f=>{const l=ba(f,u,p,h);if(f.response&&f.fetchData.__span&&Os.set(f.response,f.fetchData.__span),l){const d=Ds(f.fetchData.url),m=d?Ve(d).host:void 0;l.setAttributes({"http.url":d,"server.address":m}),i&&Rs(l),c==null||c(l,{headers:f.headers})}})),r&&Ko(f=>{var d;const l=function(m,g,b,v){const E=m.xhr,_=E==null?void 0:E[Ge];if(!E||E.__sentry_own_request__||!_)return;const{url:y,method:T}=_,x=Ee()&&g(y);if(m.endTimestamp&&x){const I=E.__sentry_xhr_span_id__;if(!I)return;const M=v[I];return void(M&&_.status_code!==void 0&&(oo(M,_.status_code),M.end(),delete v[I]))}const D=Ds(y),S=Ve(D||y),O=($=y,$.split(/[?#]/,1)[0]),R=!!X(),k=x&&R?kt({name:`${T} ${O}`,attributes:{url:y,type:"xhr","http.method":T,"http.url":D,"server.address":S==null?void 0:S.host,[B]:"auto.http.browser",[Ue]:"http.client",...(S==null?void 0:S.search)&&{"http.query":S==null?void 0:S.search},...(S==null?void 0:S.hash)&&{"http.fragment":S==null?void 0:S.hash}}}):new Fe;var $;E.__sentry_xhr_span_id__=k.spanContext().spanId,v[E.__sentry_xhr_span_id__]=k,b(y)&&function(I,M){const{"sentry-trace":z,baggage:J}=Ao({span:M});z&&function(Ce,ve,ye){var br;const se=(br=Ce.__sentry_xhr_v3__)==null?void 0:br.request_headers;if(!(se!=null&&se["sentry-trace"]))try{if(Ce.setRequestHeader("sentry-trace",ve),ye){const un=se==null?void 0:se.baggage;un&&un.split(",").some(ti=>ti.trim().startsWith("sentry-"))||Ce.setRequestHeader("baggage",ye)}}catch{}}(I,z,J)}(E,Ee()&&R?k:void 0);const P=j();return P&&P.emit("beforeOutgoingRequestSpan",k,m),k}(f,u,p,h);if(l){let m;i&&Rs(l);try{m=new Headers((d=f.xhr.__sentry_xhr_v3__)==null?void 0:d.request_headers)}catch{}c==null||c(l,{headers:m})}})}function Rs(t){const{url:e}=F(t).data;if(!e||typeof e!="string")return;const n=at("resource",({entries:r})=>{r.forEach(s=>{(function(o){return o.entryType==="resource"&&"initiatorType"in o&&typeof o.nextHopProtocol=="string"&&(o.initiatorType==="fetch"||o.initiatorType==="xmlhttprequest")})(s)&&s.name.endsWith(e)&&(function(o){const{name:i,version:a}=Yo(o.nextHopProtocol),c=[];return c.push(["network.protocol.version",a],["network.protocol.name",i]),te()?[...c,["http.request.redirect_start",oe(o.redirectStart)],["http.request.fetch_start",oe(o.fetchStart)],["http.request.domain_lookup_start",oe(o.domainLookupStart)],["http.request.domain_lookup_end",oe(o.domainLookupEnd)],["http.request.connect_start",oe(o.connectStart)],["http.request.secure_connection_start",oe(o.secureConnectionStart)],["http.request.connection_end",oe(o.connectEnd)],["http.request.request_start",oe(o.requestStart)],["http.request.response_start",oe(o.responseStart)],["http.request.response_end",oe(o.responseEnd)]]:c}(s).forEach(o=>t.setAttribute(...o)),setTimeout(n))})})}function oe(t=0){return((te()||performance.timeOrigin)+t)/1e3}function Ds(t){try{return new URL(t,L.location.origin).href}catch{return}}const Cc=3600,Ls="sentry_previous_trace",Ic="sentry.previous_trace";function $c(t,{linkPreviousTrace:e,consistentTraceSampling:n}){const r=e==="session-storage";let s=r?function(){var i;try{const a=(i=L.sessionStorage)==null?void 0:i.getItem(Ls);return JSON.parse(a)}catch{return}}():void 0;t.on("spanStart",i=>{if(V(i)!==i)return;const a=q().getPropagationContext();s=function(c,u,p){const h=F(u);function f(){var m,g;try{return Number((m=p.dsc)==null?void 0:m.sample_rate)??Number((g=h.data)==null?void 0:g[lr])}catch{return 0}}const l={spanContext:u.spanContext(),startTimestamp:h.start_timestamp,sampleRate:f(),sampleRand:p.sampleRand};if(!c)return l;const d=c.spanContext;return d.traceId===h.trace_id?c:(Date.now()/1e3-c.startTimestamp<=Cc&&(ee&&w.info(`Adding previous_trace ${d} link to span ${{op:h.op,...u.spanContext()}}`),u.addLink({context:d,attributes:{[ki]:"previous_trace"}}),u.setAttribute(Ic,`${d.traceId}-${d.spanId}-${$n(d)?1:0}`)),l)}(s,i,a),r&&function(c){try{L.sessionStorage.setItem(Ls,JSON.stringify(c))}catch(u){ee&&w.warn("Could not store previous trace in sessionStorage",u)}}(s)});let o=!0;n&&t.on("beforeSampling",i=>{if(!s)return;const a=q(),c=a.getPropagationContext();o&&c.parentSpanId?o=!1:(a.setPropagationContext({...c,dsc:{...c.dsc,sample_rate:String(s.sampleRate),sampled:String($n(s.spanContext))},sampleRand:s.sampleRand}),i.parentSampled=$n(s.spanContext),i.parentSampleRate=s.sampleRate,i.spanAttributes={...i.spanAttributes,[ro]:s.sampleRate})})}function $n(t){return t.traceFlags===1}const Pc={...Ht,instrumentNavigation:!0,instrumentPageLoad:!0,markBackgroundSpan:!0,enableLongTask:!0,enableLongAnimationFrame:!0,enableInp:!0,linkPreviousTrace:"in-memory",consistentTraceSampling:!1,_experiments:{},...Xo};let Ns=!1;const Ac=(t={})=>{Ns&&We(()=>{console.warn("Multiple browserTracingIntegration instances are not supported.")}),Ns=!0;const e=L.document;Hr||(Hr=!0,ho(Bn),mo(Bn));const{enableInp:n,enableLongTask:r,enableLongAnimationFrame:s,_experiments:{enableInteractions:o,enableStandaloneClsSpans:i},beforeStartSpan:a,idleTimeout:c,finalTimeout:u,childSpanTimeout:p,markBackgroundSpan:h,traceFetch:f,traceXHR:l,trackFetchStreamPerformance:d,shouldCreateSpanForRequest:m,enableHTTPTimings:g,instrumentPageLoad:b,instrumentNavigation:v,linkPreviousTrace:E,consistentTraceSampling:_,onRequestSpanStart:y}={...Pc,...t},T=ec({recordClsStandaloneSpans:i||!1});n&&ic(),s&&N.PerformanceObserver&&PerformanceObserver.supportedEntryTypes&&PerformanceObserver.supportedEntryTypes.includes("long-animation-frame")?new PerformanceObserver(S=>{const O=X();if(O)for(const R of S.getEntries()){if(!R.scripts[0])continue;const k=U(te()+R.startTime),{start_timestamp:$,op:P}=F(O);if(P==="navigation"&&$&&k<$)continue;const I=U(R.duration),M={[B]:"auto.ui.browser.metrics"},z=R.scripts[0],{invoker:J,invokerType:Ce,sourceURL:ve,sourceFunctionName:ye,sourceCharPosition:se}=z;M["browser.script.invoker"]=J,M["browser.script.invoker_type"]=Ce,ve&&(M["code.filepath"]=ve),ye&&(M["code.function"]=ye),se!==-1&&(M["browser.script.source_char_position"]=se),pe(O,k,k+I,{name:"Main UI thread blocked",op:"ui.long-animation-frame",attributes:M})}}).observe({type:"long-animation-frame",buffered:!0}):r&&at("longtask",({entries:S})=>{const O=X();if(!O)return;const{op:R,start_timestamp:k}=F(O);for(const $ of S){const P=U(te()+$.startTime),I=U($.duration);R==="navigation"&&k&&P<k||pe(O,P,P+I,{name:"Main UI thread blocked",op:"ui.long-task",attributes:{[B]:"auto.ui.browser.metrics"}})}}),o&&at("event",({entries:S})=>{const O=X();if(O){for(const R of S)if(R.name==="click"){const k=U(te()+R.startTime),$=U(R.duration),P={name:qe(R.target),op:`ui.interaction.${R.name}`,startTime:k,attributes:{[B]:"auto.ui.browser.metrics"}},I=Zs(R.target);I&&(P.attributes["ui.component_name"]=I),pe(O,k,k+$,P)}}});const x={name:void 0,source:void 0};function D(S,O){const R=O.op==="pageload",k=a?a(O):O,$=k.attributes||{};O.name!==k.name&&($[le]="custom",k.attributes=$),x.name=k.name,x.source=$[le];const P=Kr(k,{idleTimeout:c,finalTimeout:u,childSpanTimeout:p,disableAutoFinish:R,beforeSpanEnd:M=>{T(),tc(M,{recordClsOnPageloadSpan:!i}),js(S,void 0);const z=q(),J=z.getPropagationContext();z.setPropagationContext({...J,traceId:P.spanContext().traceId,sampled:Ne(P),dsc:xe(M)})}});function I(){e&&["interactive","complete"].includes(e.readyState)&&S.emit("idleSpanEnableAutoFinish",P)}js(S,P),R&&e&&(e.addEventListener("readystatechange",()=>{I()}),I())}return{name:"BrowserTracing",afterAllSetup(S){let O=wt();function R(){const k=jt(S);k&&!F(k).timestamp&&(ee&&w.log(`[Tracing] Finishing current active span with op: ${F(k).op}`),k.setAttribute(Yt,"cancelled"),k.end())}if(S.on("startNavigationSpan",k=>{if(j()!==S)return;R(),ke().setPropagationContext({traceId:be(),sampleRand:Math.random()});const $=q();$.setPropagationContext({traceId:be(),sampleRand:Math.random()}),$.setSDKProcessingMetadata({normalizedRequest:void 0}),D(S,{op:"navigation",...k})}),S.on("startPageLoadSpan",(k,$={})=>{if(j()!==S)return;R();const P=Ai($.sentryTrace||Ms("sentry-trace"),$.baggage||Ms("baggage")),I=q();I.setPropagationContext(P),I.setSDKProcessingMetadata({normalizedRequest:Zn()}),D(S,{op:"pageload",...k})}),E!=="off"&&$c(S,{linkPreviousTrace:E,consistentTraceSampling:_}),L.location){if(b){const k=te();(function($,P,I){$.emit("startPageLoadSpan",P,I),q().setTransactionName(P.name),jt($)})(S,{name:L.location.pathname,startTime:k?k/1e3:void 0,attributes:{[le]:"url",[B]:"auto.pageload.browser"}})}v&&yr(({to:k,from:$})=>{if($===void 0&&(O==null?void 0:O.indexOf(k))!==-1)return void(O=void 0);O=void 0;const P=Ro(k);(function(I,M){I.emit("startNavigationSpan",M),q().setTransactionName(M.name),jt(I)})(S,{name:(P==null?void 0:P.pathname)||L.location.pathname,attributes:{[le]:"url",[B]:"auto.navigation.browser"}}),q().setSDKProcessingMetadata({normalizedRequest:{...Zn(),url:k}})})}h&&(L.document?L.document.addEventListener("visibilitychange",()=>{const k=X();if(!k)return;const $=V(k);if(L.document.hidden&&$){const P="cancelled",{op:I,status:M}=F($);ee&&w.log(`[Tracing] Transaction: ${P} -> since tab moved to the background, op: ${I}`),M||$.setStatus({code:H,message:P}),$.setAttribute("sentry.cancellation_reason","document.hidden"),$.end()}}):ee&&w.warn("[Tracing] Could not set up background tab detection due to lack of global document")),o&&function(k,$,P,I,M){const z=L.document;let J;const Ce=()=>{const ve="ui.action.click",ye=jt(k);if(ye){const se=F(ye).op;if(["navigation","pageload"].includes(se))return void(ee&&w.warn(`[Tracing] Did not create ${ve} span because a pageload or navigation span is in progress.`))}J&&(J.setAttribute(Yt,"interactionInterrupted"),J.end(),J=void 0),M.name?J=Kr({name:M.name,op:ve,attributes:{[le]:M.source||"url"}},{idleTimeout:$,finalTimeout:P,childSpanTimeout:I}):ee&&w.warn(`[Tracing] Did not create ${ve} transaction because _latestRouteName is missing.`)};z&&addEventListener("click",Ce,{once:!1,capture:!0})}(S,c,u,p,x),n&&function(){const k=({entries:$})=>{const P=X(),I=P&&V(P);$.forEach(M=>{if(!function(J){return"duration"in J}(M)||!I)return;const z=M.interactionId;if(z!=null&&!zt.has(z)){if(kn.length>10){const J=kn.shift();zt.delete(J)}kn.push(z),zt.set(z,I)}})};at("event",k),at("first-input",k)}(),kc(S,{traceFetch:f,traceXHR:l,trackFetchStreamPerformance:d,tracePropagationTargets:S.getOptions().tracePropagationTargets,shouldCreateSpanForRequest:m,enableHTTPTimings:g,onRequestSpanStart:y})}}};function Ms(t){const e=L.document,n=e==null?void 0:e.querySelector(`meta[name=${t}]`);return(n==null?void 0:n.getAttribute("content"))||void 0}const Zo="_sentry_idleSpan";function jt(t){return t[Zo]}function js(t,e){Q(t,Zo,e)}class Qo extends Error{constructor(e){super(e),this.name="PerformanceException"}}class Oc extends Qo{constructor(n,r,s,o="unknown"){super(`Slow framerate detected: ${n.toFixed(1)} fps`);G(this,"fps");G(this,"threshold");G(this,"avgFramerate");G(this,"webviewId");this.name="SlowFramerateException",this.fps=n,this.threshold=r,this.avgFramerate=s,this.webviewId=o}}class Rc extends Qo{constructor(n,r,s=null,o="unknown"){super(`Slow INP detected: ${n.toFixed(1)} ms${s?` on target: ${s}`:""}`);G(this,"inp");G(this,"threshold");G(this,"target");G(this,"webviewId");this.name="SlowINPException",this.inp=n,this.threshold=r,this.target=s,this.webviewId=o}}function Dc(t){if(window.augmentPerformance=window.augmentPerformance||{},window.augmentPerformance.initialized)return;window.augmentPerformance.initialized=!0;let e=0,n=performance.now(),r=60;const s=[];let o=0;const i=t.lowFramerateThreshold,a=t.slowInpThreshold;if(requestAnimationFrame(function c(u){const p=u-n;if(e++,p>1e3){r=1e3*e/p,e=0,n=u,s.push(r),s.length>10&&s.shift();const h=s.reduce((f,l)=>f+l,0)/s.length;if(r<i){console.error(`[Augment Performance] Slow framerate detected: ${r.toFixed(1)} fps`),console.error(`[Augment Performance] Avg framerate detected: ${h.toFixed(1)} fps`);const f=r<15,l=new Oc(r,i,h,window.location.href);mt(d=>{d.setTag("performance_issue","slow_framerate"),d.setTag("fps_value",r.toFixed(1)),d.setTag("avg_fps",h.toFixed(1)),d.setTag("webview_url",window.location.href),d.setExtra("performance_data",{fps:r,avgFps:h,threshold:i,isCritical:f,framerateHistory:[...s]}),d.setLevel("warning"),Gn(l)})}}requestAnimationFrame(c)}),PerformanceObserver.supportedEntryTypes.includes("event"))try{new PerformanceObserver(c=>{(u=>{const p=u.getEntries().filter(l=>"interactionId"in l&&"duration"in l&&l.startTime>0&&l.duration<1e6);if(p.length===0)return;p.sort((l,d)=>d.duration-l.duration);const h=Math.floor(.98*p.length),f=p[Math.min(h,p.length-1)].duration;if(f>a){console.error(`[Augment Performance] Slow INP detected: ${f.toFixed(1)} ms`);let l=null;const d=p[0];d&&"target"in d&&(l=d.target,console.error("[Augment Performance] Slow interaction target:",l,d));const m=new Rc(f,a,l?String(l):null,window.location.href);mt(g=>{g.setTag("performance_issue","slow_inp"),g.setTag("inp_value",f.toFixed(1)),g.setTag("webview_url",window.location.href),l&&g.setTag("interaction_target",String(l)),g.setExtra("performance_data",{inp:f,threshold:a,target:l}),g.setLevel("warning"),Gn(m)}),f>o&&(o=f)}})(c)}).observe({entryTypes:["event","first-input"],buffered:!0})}catch(c){console.error("[Augment Performance] Error setting up INP monitoring:",c)}else console.warn("[Augment Performance] PerformanceObserver not supported for INP monitoring");window.augmentPerformance.getFramerate=()=>r,window.augmentPerformance.getWorstINP=()=>o}const Fs=16,qs=200;function Us(){var t;return((t=window.augmentFlags)==null?void 0:t.enablePerformanceMonitoring)??!1}let Hs=!1;(function(){var n,r;const t=!!((r=(n=window.augmentFlags)==null?void 0:n.sentry)!=null&&r.enabled);var e;(e={enabled:Us(),lowFramerateThreshold:Fs,slowInpThreshold:qs}).enabled&&Dc({lowFramerateThreshold:e.lowFramerateThreshold||Fs,slowInpThreshold:e.slowInpThreshold||qs}),Us()&&!t&&console.warn("[Augment Performance] Performance monitoring enabled but Sentry is not initialized. Performance issues will not be reported to Sentry.")})(),function(){var e,n;if(!((n=(e=window.augmentFlags)==null?void 0:e.sentry)!=null&&n.enabled))return;const t=window.augmentFlags.sentry;if(t)if(Hs)console.warn("Sentry is already initialized, duplicate initialization attempt");else try{(function(r){const s={...r};Po(s,"svelte"),Tc(s)})({dsn:t.dsn,release:t.release,environment:t.environment,tracesSampleRate:t.tracesSampleRate||0,replaysSessionSampleRate:t.replaysSessionSampleRate||0,replaysOnErrorSampleRate:t.replaysOnErrorSampleRate||0,sampleRate:t.errorSampleRate||0,sendDefaultPii:t.sendDefaultPii!==void 0&&t.sendDefaultPii,integrations:(()=>{const r=[];return t.tracesSampleRate&&t.tracesSampleRate>0&&r.push(Ac()),r})(),beforeSend:r=>r}),t.tags&&Object.entries(t.tags).forEach(([r,s])=>{(function(o,i){ke().setTag(o,i)})(r,String(s))}),Hs=!0}catch(r){console.error("Failed to initialize Sentry:",r)}else console.warn("Sentry configuration not found in window.augmentDeps")}();var Lc=St('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.7816 4.03157C12.0062 3.80702 12.0062 3.44295 11.7816 3.2184C11.5571 2.99385 11.193 2.99385 10.9685 3.2184L7.50005 6.68682L4.03164 3.2184C3.80708 2.99385 3.44301 2.99385 3.21846 3.2184C2.99391 3.44295 2.99391 3.80702 3.21846 4.03157L6.68688 7.49999L3.21846 10.9684C2.99391 11.193 2.99391 11.557 3.21846 11.7816C3.44301 12.0061 3.80708 12.0061 4.03164 11.7816L7.50005 8.31316L10.9685 11.7816C11.193 12.0061 11.5571 12.0061 11.7816 11.7816C12.0062 11.557 12.0062 11.193 11.7816 10.9684L8.31322 7.49999L11.7816 4.03157Z" fill="currentColor"></path></svg>');function Zc(t){var e=Lc();he(t,e)}var Nc=sr("<div><!></div>");const Qc={Root:mi,IconButton:function(t,e){const n=lt(e,["children","$$slots","$$events","$$legacy"]),r=lt(n,["color","highContrast","disabled"]);Bs(e,!1);const s=Rn(),o=Rn(),i=si(hi.CONTEXT_KEY);let a=de(e,"color",24,()=>{return d=i.color,m="neutral",typeof d=="string"&&["accent","neutral","error","success","warning","info"].includes(d)?d:m;var d,m}),c=de(e,"highContrast",8,!1),u=de(e,"disabled",8,!1),p=i.size===0?.5:i.size;zs(()=>($e(s),$e(o),Dn(r)),()=>{On(s,r.class),On(o,oi(r,["class"]))}),Ws(),ii();var h=Nc(),f=Re(h);const l=Ln(()=>`c-badge-icon-btn__base-btn ${$e(s)}`);gi(f,ai({get size(){return p},variant:"ghost",get color(){return a()},get highContrast(){return c()},get disabled(){return u()},get class(){return $e(l)}},()=>$e(o),{$$events:{click(d){ie.call(this,e,d)},keyup(d){ie.call(this,e,d)},keydown(d){ie.call(this,e,d)},mousedown(d){ie.call(this,e,d)},mouseover(d){ie.call(this,e,d)},focus(d){ie.call(this,e,d)},mouseleave(d){ie.call(this,e,d)},blur(d){ie.call(this,e,d)},contextmenu(d){ie.call(this,e,d)}},children:(d,m)=>{var g=ci(),b=Js(g);ui(b,e,"default",{},null),he(d,g)},$$slots:{default:!0}})),Nn(()=>it(h,1,di(()=>`c-badge-icon-btn c-badge-icon-btn--${i.variant} c-badge-icon-btn--size-${p}`),"svelte-1im94um")),he(t,h),Gs()}};var Mc=sr("<span> </span> <span> </span>",1),jc=sr('<label><!> <input type="checkbox" role="switch"/></label>');function eu(t,e){Bs(e,!1);const n=Rn();let r=de(e,"checked",12,!1),s=de(e,"disabled",8,!1),o=de(e,"size",8,2),i=de(e,"ariaLabel",24,()=>{}),a=de(e,"onText",24,()=>{}),c=de(e,"offText",24,()=>{});zs(()=>(Dn(a()),Dn(c())),()=>{On(n,a()||c())}),Ws();var u=jc();let p;var h=Re(u),f=m=>{var g=Mc(),b=Js(g);let v;var E=Re(b),_=Sr(b,2);let y;var T=Re(_);Nn((x,D)=>{v=it(b,1,"c-toggle-text c-toggle-text--off svelte-xr5g0k",null,v,x),Er(E,c()||""),y=it(_,1,"c-toggle-text c-toggle-text--on svelte-xr5g0k",null,y,D),Er(T,a()||"")},[()=>({visible:!r()&&c()}),()=>({visible:r()&&a()})],Ln),he(m,g)};li(h,m=>{$e(n)&&m(f)});var l=Sr(h,2);let d;Nn((m,g)=>{p=it(u,1,`c-toggle-track c-toggle-track-size--${o()??""}`,"svelte-xr5g0k",p,m),d=it(l,1,"c-toggle-input svelte-xr5g0k",null,d,g),l.disabled=s(),pi(l,"aria-label",i())},[()=>({checked:r(),disabled:s(),"has-text":$e(n)}),()=>({disabled:s()})],Ln),_i(l,r),wr("keydown",l,function(m){s()||m.key!=="Enter"&&m.key!==" "||(m.preventDefault(),r(!r()))}),wr("change",u,function(m){ie.call(this,e,m)}),he(t,u),Gs()}const Pn={enabled:!1,volume:.5},tu={enabled:!0},Fc=""+new URL("agent-complete-DO0gyADk.mp3",import.meta.url).href;var ei=(t=>(t.AGENT_COMPLETE="agent-complete",t))(ei||{});const qc={"agent-complete":Fc},Ae=class Ae{constructor(){G(this,"audioCache",new Map)}static getInstance(){return Ae._instance||(Ae._instance=new Ae),Ae._instance}retrieveAudioElement(e,n){let r=this.audioCache.get(e);return r?r.volume=n.volume:(r=new Audio,r.src=qc[e],r.volume=n.volume,r.preload="auto",r._isUnlocked=!1,this.audioCache.set(e,r)),r}async playSound(e,n){if(n.enabled)try{const r=this.retrieveAudioElement(e,n);r.currentTime=0,await r.play()}catch(r){if(r instanceof DOMException&&r.name==="NotAllowedError")return void console.error("Audio blocked by browser policy. Sound will work after user interaction.");console.error("Failed to play sound:",r)}}async unlockSoundForConfig(e){if(!e.enabled)return;const n=this.retrieveAudioElement("agent-complete",e);if(!n._isUnlocked)try{await this.playSound("agent-complete",{enabled:!0,volume:0}),n._isUnlocked=!0}catch(r){console.warn("Failed to unlock sound:",r)}}disposeSounds(){this.audioCache.forEach(e=>{e.pause(),e.src="",e._isUnlocked=!1}),this.audioCache.clear()}};G(Ae,"_instance");let rr=Ae;const An=rr.getInstance();class Uc{constructor(e){G(this,"_soundSettings",fi(Pn));G(this,"_isLoaded",!1);G(this,"dispose",()=>{An.disposeSounds()});this._msgBroker=e,this.initialize()}async refreshSettings(){try{const e=await this._msgBroker.sendToSidecar({type:ln.getSoundSettings});e.data&&this._soundSettings.set(e.data)}catch(e){console.warn("Failed to refresh sound settings:",e)}}async unlockSound(){dn(this._soundSettings).enabled&&An.unlockSoundForConfig(dn(this._soundSettings))}async playAgentComplete(){const e=dn(this._soundSettings);await An.playSound(ei.AGENT_COMPLETE,e)}get getCurrentSettings(){return this._soundSettings}async initialize(){if(!this._isLoaded)try{const e=await this._msgBroker.sendToSidecar({type:ln.getSoundSettings});e.data&&this._soundSettings.set(e.data),this._isLoaded=!0}catch(e){console.warn("Failed to load sound settings, using defaults:",e),this._soundSettings.set(Pn),this._isLoaded=!0}}async updateSettings(e){try{await this._msgBroker.sendToSidecar({type:ln.updateSoundSettings,data:e}),this._soundSettings.update(n=>({...n,...e}))}catch(n){throw console.error("Failed to update sound settings:",n),n}}async setEnabled(e){await this.updateSettings({enabled:e})}async setVolume(e){const n=Math.max(0,Math.min(1,e));await this.updateSettings({volume:n})}async resetToDefaults(){await this.updateSettings(Pn)}updateEnabled(e){this.setEnabled(e).catch(n=>{console.error("Failed to update enabled setting:",n)})}updateVolume(e){this.setVolume(e).catch(n=>{console.error("Failed to update volume setting:",n)})}}G(Uc,"key","soundModel");var Hc=St("<svg><!></svg>");function nu(t,e){const n=lt(e,["children","$$slots","$$events","$$legacy"]);var r=Hc();or(r,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 448 512",...n}));var s=Re(r);ir(s,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M384 336H192c-8.8 0-16-7.2-16-16V64c0-8.8 7.2-16 16-16h140.1l67.9 67.9V320c0 8.8-7.2 16-16 16m-192 48h192c35.3 0 64-28.7 64-64V115.9c0-12.7-5.1-24.9-14.1-33.9l-67.8-67.9c-9-9-21.2-14.1-33.9-14.1H192c-35.3 0-64 28.7-64 64v256c0 35.3 28.7 64 64 64M64 128c-35.3 0-64 28.7-64 64v256c0 35.3 28.7 64 64 64h192c35.3 0 64-28.7 64-64v-32h-48v32c0 8.8-7.2 16-16 16H64c-8.8 0-16-7.2-16-16V192c0-8.8 7.2-16 16-16h32v-48z"/>',!0),he(t,r)}var Bc=St("<svg><!></svg>");function ru(t,e){const n=lt(e,["children","$$slots","$$events","$$legacy"]);var r=Bc();or(r,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 448 512",...n}));var s=Re(r);ir(s,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M248 72c0-13.3-10.7-24-24-24s-24 10.7-24 24v160H40c-13.3 0-24 10.7-24 24s10.7 24 24 24h160v160c0 13.3 10.7 24 24 24s24-10.7 24-24V280h160c13.3 0 24-10.7 24-24s-10.7-24-24-24H248z"/>',!0),he(t,r)}var zc=St('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.49933 0.25C3.49635 0.25 0.25 3.49593 0.25 7.50024C0.25 10.703 2.32715 13.4206 5.2081 14.3797C5.57084 14.446 5.70302 14.2222 5.70302 14.0299C5.70302 13.8576 5.69679 13.4019 5.69323 12.797C3.67661 13.235 3.25112 11.825 3.25112 11.825C2.92132 10.9874 2.44599 10.7644 2.44599 10.7644C1.78773 10.3149 2.49584 10.3238 2.49584 10.3238C3.22353 10.375 3.60629 11.0711 3.60629 11.0711C4.25298 12.1788 5.30335 11.8588 5.71638 11.6732C5.78225 11.205 5.96962 10.8854 6.17658 10.7043C4.56675 10.5209 2.87415 9.89918 2.87415 7.12104C2.87415 6.32925 3.15677 5.68257 3.62053 5.17563C3.54576 4.99226 3.29697 4.25521 3.69174 3.25691C3.69174 3.25691 4.30015 3.06196 5.68522 3.99973C6.26337 3.83906 6.8838 3.75895 7.50022 3.75583C8.1162 3.75895 8.73619 3.83906 9.31523 3.99973C10.6994 3.06196 11.3069 3.25691 11.3069 3.25691C11.7026 4.25521 11.4538 4.99226 11.3795 5.17563C11.8441 5.68257 12.1245 6.32925 12.1245 7.12104C12.1245 9.9063 10.4292 10.5192 8.81452 10.6985C9.07444 10.9224 9.30633 11.3648 9.30633 12.0413C9.30633 13.0102 9.29742 13.7922 9.29742 14.0299C9.29742 14.2239 9.42828 14.4496 9.79591 14.3788C12.6746 13.4179 14.75 10.7025 14.75 7.50024C14.75 3.49593 11.5036 0.25 7.49933 0.25Z" fill="currentColor"></path></svg>');function su(t){var e=zc();he(t,e)}var Wc=St("<svg><!></svg>");function ou(t,e){const n=lt(e,["children","$$slots","$$events","$$legacy"]);var r=Wc();or(r,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...n}));var s=Re(r);ir(s,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M280 24c0-13.3-10.7-24-24-24s-24 10.7-24 24v270.1l-95-95c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9L239 369c9.4 9.4 24.6 9.4 33.9 0L409 233c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-95 95zM128.8 304H64c-35.3 0-64 28.7-64 64v80c0 35.3 28.7 64 64 64h384c35.3 0 64-28.7 64-64v-80c0-35.3-28.7-64-64-64h-64.8l-48 48H448c8.8 0 16 7.2 16 16v80c0 8.8-7.2 16-16 16H64c-8.8 0-16-7.2-16-16v-80c0-8.8 7.2-16 16-16h112.8zM432 408a24 24 0 1 0-48 0 24 24 0 1 0 48 0"/>',!0),he(t,r)}export{Qc as B,Zc as C,tu as D,su as G,ru as P,Uc as S,eu as T,nu as a,ou as b};
