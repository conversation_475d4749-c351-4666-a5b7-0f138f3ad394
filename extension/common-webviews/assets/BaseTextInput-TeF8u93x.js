import{i as k,aj as h,P as b,ak as m,A as y,B as i,C as $,I as z,J as B,K as S,a as A,H as C,M as E,m as j,T as M,V,N as D,R as G,b as p,t as H,S as I,D as J,L as o,G as K}from"./SpinnerAugment-kH3m-zOb.js";import"./IconButtonAugment-C8Qb_O9b.js";function T(a,e,l=e){var r=k();h(a,"input",t=>{var s=t?a.defaultValue:a.value;if(s=n(a)?d(s):s,l(s),r&&s!==(s=e())){var u=a.selectionStart,c=a.selectionEnd;a.value=s??"",c!==null&&(a.selectionStart=u,a.selectionEnd=Math.min(c,a.value.length))}}),b(e)==null&&a.value&&l(n(a)?d(a.value):a.value),m(()=>{var t=e();n(a)&&t===d(a.value)||(a.type!=="date"||t||a.value)&&t!==a.value&&(a.value=t??"")})}function q(a,e,l=e){h(a,"change",r=>{var t=r?a.defaultChecked:a.checked;l(t)}),b(e)==null&&l(a.checked),m(()=>{var r=e();a.checked=!!r})}function n(a){var e=a.type;return e==="number"||e==="range"}function d(a){return a===""?null:+a}var L=S("<div><!></div>");function w(a,e){y(e,!1);const l=j();let r=i(e,"variant",8,"surface"),t=i(e,"size",8,2),s=i(e,"type",8,"default"),u=i(e,"color",24,()=>{});$(()=>(K(u()),o),()=>{J(l,u()?o(u()):o("accent"))}),z(),B();var c=L();A(c,v=>({...C(l),class:`c-base-text-input c-base-text-input--${r()} c-base-text-input--size-${t()}`,[E]:v}),[()=>({"c-base-text-input--has-color":u()!==void 0})],"svelte-1mx5zy6");var x=H(c);M(x,{get type(){return s()},get size(){return t()},children:(v,N)=>{var f=V(),g=D(f);G(g,e,"default",{},null),p(v,f)},$$slots:{default:!0}}),p(a,c),I()}export{w as B,q as a,T as b};
