import{z as L,l as C,A as M,B as s,C as N,D as m,m as q,F as O,G as P,H as l,I as S,J as j,K as u,a as Q,L as R,M as T,T as k,N as w,O as E,P as U,Q as V,R as b,t as r,b as v,S as W}from"./SpinnerAugment-kH3m-zOb.js";var X=u('<div class="c-callout-icon svelte-1u5qnh6"><!></div>'),Y=u('<!> <div class="c-callout-body svelte-1u5qnh6"><!></div>',1),Z=u("<div><!></div>");function B(p,a){const y=L(a),x=C(a,["children","$$slots","$$events","$$legacy"]),t=C(x,["color","variant","size","highContrast"]);M(a,!1);const c=q(),o=q();let $=s(a,"color",8,"info"),A=s(a,"variant",8,"soft"),d=s(a,"size",8,2),D=s(a,"highContrast",8,!1);const F=d();N(()=>(l(c),l(o),P(t)),()=>{m(c,t.class),m(o,O(t,["class"]))}),S(),j();var n=Z();Q(n,(i,h)=>({...i,class:`c-callout c-callout--${$()} c-callout--${A()} c-callout--size-${d()} ${l(c)}`,...l(o),[T]:h}),[()=>R($()),()=>({"c-callout--highContrast":D()})],"svelte-1u5qnh6");var G=r(n);k(G,{get size(){return F},children:(i,h)=>{var f=Y(),g=w(f),H=e=>{var z=X(),K=r(z);b(K,a,"icon",{},null),v(e,z)};E(g,e=>{U(()=>y.icon)&&e(H)});var I=V(g,2),J=r(I);b(J,a,"default",{},null),v(i,f)},$$slots:{default:!0}}),v(p,n),W()}export{B as C};
