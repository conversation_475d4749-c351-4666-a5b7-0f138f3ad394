import{l as Ae,f as ke,a as De,t as b,b as n,A as fe,B as ie,al as ze,w as ee,C as me,D as g,$ as U,m as h,H as e,G as pe,I as He,J as he,V as Re,N as ne,O as te,S as $e,W as Ie,K,an as Me,X as j,Y as Le,Z as be,P as W,_ as N,Q as se,a0 as _e,T as Te,ai as Pe,ab as We,am as qe,aB as Ue}from"./SpinnerAugment-kH3m-zOb.js";import"./design-system-init-IgXUmngh.js";import{h as Ve,c as V,W as ye,e as je,i as Ke}from"./IconButtonAugment-C8Qb_O9b.js";import{O as Qe}from"./OpenFileButton-CgBtRJfx.js";import{S as Xe}from"./TextAreaAugment-IsRYkfgU.js";import{C as Ye}from"./check-C8b2LRem.js";import{C as Ee,E as Oe,D as q,M as ge,f as Ze,g as Je,h as et}from"./index-BnlWKkvq.js";import{M as we}from"./message-broker-CwcPcQ_e.js";import{R as tt,M as st}from"./rules-model-Cj5etjy1.js";import{B as Ge}from"./ButtonAugment-BZfc2Zk1.js";import{C as Fe}from"./chevron-down-BbSBSK7f.js";import{F as ot}from"./Filespan-B9x3U15q.js";import{T as Se,a as re}from"./CardAugment-BAmr_-U4.js";import"./chat-context-nrEZUHNl.js";import"./index-C4gKbsWy.js";import"./index-D-fDrvnq.js";import"./remote-agents-client-TVS4i5h_.js";import"./types-CGlLNakm.js";import"./ra-diff-ops-model-dZccOtNT.js";import"./BaseTextInput-TeF8u93x.js";import"./async-messaging-CCLqHBoR.js";import"./file-paths-Bl_IgYJd.js";var at=ke("<svg><!></svg>");function Ce(Q,z){const $=Ae(z,["children","$$slots","$$events","$$legacy"]);var I=at();De(I,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 448 512",...$}));var i=b(I);Ve(i,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M440.6 273.4c4.7-4.5 7.4-10.8 7.4-17.4s-2.7-12.8-7.4-17.4l-176-168c-9.6-9.2-24.8-8.8-33.9.8s-8.8 24.8.8 33.9L364.1 232H24c-13.3 0-24 10.7-24 24s10.7 24 24 24h340.1L231.4 406.6c-9.6 9.2-9.9 24.3-.8 33.9s24.3 9.9 33.9.8l176-168z"/>',!0),n(Q,I)}var nt=K("<!> <!>",1),rt=K('<div class="rules-dropdown-content svelte-18wohv"><!> <!></div>'),it=K("<!> <!>",1);function lt(Q,z){fe(z,!1);const[$,I]=Ie(),i=()=>U(s,"$rulesFiles",$),A=()=>U(L,"$selectedRule",$),y=()=>U(e(G),"$focusedIndex",$),M=h(),X=h(),Y=h();let oe=ie(z,"onRuleSelected",8),ae=ie(z,"disabled",8,!1);const le=new we(V),ce=new Ee,l=new Oe(V,le,ce),s=ee([]),w=ee(!0),L=ee(void 0);let G=h(void 0),k=h(()=>{});ze(()=>{(async function(){try{w.set(!0);const u=await l.findRules("",100);s.set(u)}catch(u){console.error("Failed to load rules:",u),s.set([])}finally{w.set(!1)}})();const d=u=>{var T;((T=u.data)==null?void 0:T.type)===ye.getRulesListResponse&&(s.set(u.data.data||[]),w.set(!1))};return window.addEventListener("message",d),()=>{window.removeEventListener("message",d)}});let D=h(),C=h(!1);function de(d){g(C,d)}me(()=>i(),()=>{g(M,i().length>0)}),me(()=>(pe(ae()),e(M)),()=>{g(X,ae()||!e(M))}),me(()=>e(M),()=>{g(Y,e(M)?"Move highlighted text to a .augment/rules file":"Please add at least 1 file to .augment/rules and reload VSCode")}),He(),he();var c=Re(),Z=ne(c),J=d=>{var u=Re(),T=ne(u),ue=v=>{q.Root(v,{onOpenChange:de,get requestClose(){return e(k)},set requestClose(a){g(k,a)},get focusedIndex(){return e(G)},set focusedIndex(a){_e(g(G,a),"$focusedIndex",$)},children:(a,m)=>{var f=it(),p=ne(f);q.Trigger(p,{children:(E,ve)=>{const _=N(()=>(pe(re),W(()=>[re.Hover]))),P=N(()=>!e(C)&&void 0);Me(Se(E,{get content(){return e(Y)},get triggerOn(){return e(_)},side:"top",get open(){return e(P)},children:(O,x)=>{Ge(O,{color:"neutral",variant:"soft",size:1,get disabled(){return e(X)},children:(r,o)=>{var R=j();Le(()=>be(R,(A(),W(()=>A()?A().path:"Rules")))),n(r,R)},$$slots:{default:!0,iconLeft:(r,o)=>{Ce(r,{slot:"iconLeft"})},iconRight:(r,o)=>{Fe(r,{slot:"iconRight"})}}})},$$slots:{default:!0},$$legacy:!0}),O=>g(D,O),()=>e(D))},$$slots:{default:!0}});var H=se(p,2);q.Content(H,{side:"bottom",align:"start",children:(E,ve)=>{var _=rt(),P=b(_);je(P,1,i,Ke,(r,o,R)=>{const B=N(()=>y()===R);q.Item(r,{onSelect:()=>function(F){L.set(F),oe()(F),e(k)()}(e(o)),get highlight(){return e(B)},children:(F,S)=>{ot(F,{get filepath(){return e(o),W(()=>e(o).path)}})},$$slots:{default:!0}})});var O=se(P,2),x=r=>{var o=nt(),R=ne(o);q.Separator(R,{});var B=se(R,2);q.Label(B,{children:(F,S)=>{Te(F,{size:1,color:"neutral",children:(Be,vt)=>{var xe=j();Le(Ne=>be(xe,Ne),[()=>(i(),y(),W(()=>`Move to ${i()[y()].path}`))],N),n(Be,xe)},$$slots:{default:!0}})},$$slots:{default:!0}}),n(r,o)};te(O,r=>{y(),i(),W(()=>y()!==void 0&&i()[y()])&&r(x)}),n(E,_)},$$slots:{default:!0}}),n(a,f)},$$slots:{default:!0},$$legacy:!0})},t=v=>{const a=N(()=>(pe(re),W(()=>[re.Hover])));Me(Se(v,{get content(){return e(Y)},get triggerOn(){return e(a)},side:"top",children:(m,f)=>{Ge(m,{color:"neutral",variant:"soft",size:1,disabled:!0,children:(p,H)=>{var E=j("Rules");n(p,E)},$$slots:{default:!0,iconLeft:(p,H)=>{Ce(p,{slot:"iconLeft"})},iconRight:(p,H)=>{Fe(p,{slot:"iconRight"})}}})},$$slots:{default:!0},$$legacy:!0}),m=>g(D,m),()=>e(D))};te(T,v=>{e(M)?v(ue):v(t,!1)}),n(d,u)};te(Z,d=>{U(w,"$loading",$)||d(J)}),n(Q,c),$e(),I()}var ct=K('<div slot="iconLeft" class="c-move-text-btn__left_icon svelte-1yddhs6"><!></div>'),dt=K('<div class="l-file-controls svelte-1yddhs6" slot="header"><div class="l-file-controls-left svelte-1yddhs6"><div class="c-move-text-btn svelte-1yddhs6"><!></div> <div class="c-move-text-btn svelte-1yddhs6"><!></div></div> <div class="l-file-controls-right svelte-1yddhs6"><!></div></div>'),ut=K('<div class="c-memories-container svelte-1vchs21"><!></div>');Ue(function(Q,z){fe(z,!1);const[$,I]=Ie(),i=()=>U(M,"$editorContent",$),A=()=>U(X,"$editorPath",$),y=new we(V),M=ee(null),X=ee(null),Y={handleMessageFromExtension(l){const s=l.data;if(s&&s.type===ye.loadFile){if(s.data.content!==void 0){const w=s.data.content.replace(/^\n+/,"");M.set(w)}s.data.pathName&&X.set(s.data.pathName)}return!0}};ze(()=>{y.registerConsumer(Y),V.postMessage({type:ye.memoriesLoaded})}),Pe(()=>{y.dispose()}),he();var oe=ut();We("message",qe,function(...l){var s;(s=y.onMessageFromExtension)==null||s.apply(this,l)});var ae=b(oe),le=l=>{(function(s,w){fe(w,!1);let L=ie(w,"text",12),G=ie(w,"path",8);const k=new we(V),D=new Ee,C=new Oe(V,k,D),de=new tt(k);let c=h(""),Z=h(0),J=h(0),d=h("neutral");const u=async()=>{G()&&C.saveFile({repoRoot:"",pathName:G(),content:L()})};async function T(t){if(!e(c))return;let v,a,m;const f=e(c).slice(0,20);if(t==="userGuidelines"?(v="Move Content to User Guidelines",a=`Are you sure you want to move the selected content "${f}" to your user guidelines?`,m=ge.userGuidelines):t==="augmentGuidelines"?(v="Move Content to Workspace Guidelines",a=`Are you sure you want to move the selected content "${f}" to workspace guidelines?`,m=ge.augmentGuidelines):(v="Move Content to Rule",a=`Are you sure you want to move the selected content "${f}" to rule file "${t.rule.path}"?`,m=ge.rules),!await C.openConfirmationModal({title:v,message:a,confirmButtonText:"Move",cancelButtonText:"Cancel"}))return;t==="userGuidelines"?C.updateUserGuidelines(e(c)):t==="augmentGuidelines"?C.updateWorkspaceGuidelines(e(c)):(await de.updateRuleContent({type:t.rule.type,path:t.rule.path,content:t.rule.content+`

`+e(c),description:t.rule.description}),C.showNotification({message:`Moved content "${f}" to rule file "${t.rule.path}"`,type:"info",openFileMessage:{repoRoot:"",pathName:`${Je}/${et}/${t.rule.path}`}}));const p=L().substring(0,e(Z))+L().substring(e(J));return L(p),await u(),C.reportAgentSessionEvent({eventName:Ze.memoriesMove,conversationId:"",eventData:{memoriesMoveData:{target:m}}}),"success"}async function ue(t){await T({rule:t})}he(),st(s,{saveFunction:u,variant:"surface",size:2,resize:"vertical",class:"markdown-editor",get selectedText(){return e(c)},set selectedText(t){g(c,t)},get selectionStart(){return e(Z)},set selectionStart(t){g(Z,t)},get selectionEnd(){return e(J)},set selectionEnd(t){g(J,t)},get value(){return L()},set value(t){L(t)},$$slots:{header:(t,v)=>{var a=dt(),m=b(a),f=b(m),p=b(f);const H=N(()=>!e(c));Xe(p,{tooltip:{neutral:"Move highlighted text to user guidelines",success:"Text moved to user guidelines"},stateVariant:{success:"solid",neutral:"soft"},defaultColor:"neutral",onClick:()=>T("userGuidelines"),get disabled(){return e(H)},stickyColor:!1,persistOnTooltipClose:!0,replaceIconOnSuccess:!0,size:1,get state(){return e(d)},set state(x){g(d,x)},children:(x,r)=>{var o=j("User Guidelines");n(x,o)},$$slots:{default:!0,iconLeft:(x,r)=>{var o=ct(),R=b(o),B=S=>{Ye(S,{})},F=S=>{Ce(S,{})};te(R,S=>{e(d)==="success"?S(B):S(F,!1)}),n(x,o)}},$$legacy:!0});var E=se(f,2),ve=b(E);const _=N(()=>!e(c));lt(ve,{onRuleSelected:ue,get disabled(){return e(_)}});var P=se(m,2),O=b(P);Qe(O,{size:1,get path(){return G()},variant:"soft",onOpenLocalFile:async()=>(C.openFile({repoRoot:"",pathName:G()}),"success"),$$slots:{text:(x,r)=>{Te(x,{slot:"text",size:1,children:(o,R)=>{var B=j("Augment-Memories.md");n(o,B)},$$slots:{default:!0}})}}}),n(t,a)}},$$legacy:!0}),$e()})(l,{get text(){return i()},get path(){return A()}})},ce=l=>{var s=j("Loading memories...");n(l,s)};te(ae,l=>{i()!==null&&A()!==null?l(le):l(ce,!1)}),n(Q,oe),$e(),I()},{target:document.getElementById("app")});
