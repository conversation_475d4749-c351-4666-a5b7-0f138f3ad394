import{l as Ye,f as Ze,a as Qe,t as _,b as n,A as ye,B as y,m as C,ai as Me,J as Se,K as h,T as ae,X as ne,Y as M,Z as ce,Q as u,N as G,O,P as r,G as S,H as e,S as ke,D as A,C as Z,I as qe,_ as x,a5 as Ge,V as et,a7 as tt,ab as Ue,u as Ee,$ as Xe,W as Ne,aA as st,v as Je,al as at,am as nt,aB as rt}from"./SpinnerAugment-kH3m-zOb.js";import"./design-system-init-IgXUmngh.js";/* empty css                                */import{h as it,I as Be,e as me,i as ot,c as lt}from"./IconButtonAugment-C8Qb_O9b.js";import{M as dt}from"./message-broker-CwcPcQ_e.js";import{S as ct,T as vt,a as Ke,b as gt,c as je,d as ut,v as mt,e as pt}from"./StatusIndicator-C48pvTfC.js";import{a as Y,s as wt,R as Oe}from"./index-C4gKbsWy.js";import{T as Te,a as de,C as ht}from"./CardAugment-BAmr_-U4.js";import{C as ft}from"./CalloutAugment-BUpCn_TI.js";import{E as _t}from"./exclamation-triangle-C_LrR9gu.js";import{d as $t,s as yt,R as He}from"./remote-agents-client-TVS4i5h_.js";import{A as St}from"./augment-logo-C7lllHv9.js";import"./async-messaging-CCLqHBoR.js";import"./types-CGlLNakm.js";var kt=Ze("<svg><!></svg>"),bt=h(" <!>",1),xt=h('<div class="agent-card-footer svelte-1qwlkoj"><!> <div class="time-container"><!></div></div>'),At=h('<div class="task-text-container svelte-1tatwxk"><!></div>'),Pt=h('<div class="task-status-indicator svelte-1tatwxk"><!></div>'),Rt=h('<div class="task-item svelte-1tatwxk"><div></div> <!> <!></div>'),zt=h(' <button class="error-dismiss svelte-1bxdvw4" aria-label="Dismiss error">×</button>',1),It=h('<div class="deletion-error svelte-1bxdvw4"><!></div>'),Ft=h('<span class="setup-script-title svelte-1bxdvw4">Generate a setup script</span>'),Ot=h('<div class="setup-script-title-container svelte-1bxdvw4"><div class="setup-script-badge svelte-1bxdvw4"><!></div> <!></div>'),Tt=h('<div class="tasks-list svelte-1bxdvw4"></div>'),Ht=h('<div class="card-header svelte-1bxdvw4"><div class="session-summary-container svelte-1bxdvw4"><!></div> <div class="card-info"><!></div></div> <div class="card-content svelte-1bxdvw4"><!></div> <div class="card-actions svelte-1bxdvw4"><!> <!> <!></div> <!>',1),qt=h("<div><!> <!></div>");function Et(ve,$){ye($,!1);const P=C(),q=C();let i=y($,"agent",8),J=y($,"selected",8,!1),K=y($,"isPinned",8,!1),W=y($,"onSelect",8),T=y($,"onDelete",8),R=y($,"deletionError",12,null),H=y($,"isDeleting",8,!1),L=y($,"onTogglePinned",8),U=y($,"onSSH",24,()=>{}),v=y($,"canSsh",8,!1);function B(){R(null)}Me(()=>{B()}),Z(()=>S(i()),()=>{A(P,i().turn_summaries||[])}),Z(()=>{},()=>{A(q,!0)}),qe(),Se();var E=qt();let d;var m=_(E),Q=z=>{var k=It(),F=_(k);ft(F,{variant:"soft",color:"error",size:1,children:(re,c)=>{var l=zt(),j=G(l),ie=u(j);M(()=>ce(j,`${R()??""} `)),Ue("click",ie,B),n(re,l)},$$slots:{default:!0,icon:(re,c)=>{_t(re,{slot:"icon"})}}}),n(z,k)};O(m,z=>{R()&&z(Q)});var I=u(m,2);ht(I,{variant:"surface",size:2,interactive:!0,class:"agent-card",$$events:{click:()=>W()(i().remote_agent_id),keydown:z=>z.key==="Enter"&&W()(i().remote_agent_id)},children:(z,k)=>{var F=Ht(),re=G(F),c=_(re),l=_(c),j=a=>{var o=Ot(),g=_(o),s=_(g);Ke(s);var t=u(g,2);ae(t,{size:2,weight:"medium",children:(f,w)=>{var ee=Ft();n(f,ee)},$$slots:{default:!0}}),n(a,o)},ie=a=>{ae(a,{size:2,weight:"medium",class:"session-text",children:(o,g)=>{var s=ne();M(()=>ce(s,(S(i()),r(()=>i().session_summary)))),n(o,s)},$$slots:{default:!0}})};O(l,a=>{S(i()),r(()=>i().is_setup_script_agent)?a(j):a(ie,!1)});var X=u(c,2),D=_(X);ct(D,{get status(){return S(i()),r(()=>i().status)},get workspaceStatus(){return S(i()),r(()=>i().workspace_status)},isExpanded:!0,get hasUpdates(){return S(i()),r(()=>i().has_updates)}});var oe=u(re,2),ge=_(oe),be=a=>{var o=Tt();me(o,5,()=>(e(P),r(()=>e(P).slice(0,3))),ot,(g,s)=>{(function(t,f){ye(f,!1);const w=C();let ee=y(f,"text",8),b=y(f,"status",8,"info");Z(()=>S(b()),()=>{A(w,function(se){switch(se){case"success":return"task-success";case"warning":return"task-warning";case"error":return"task-error";default:return"task-info"}}(b()))}),qe(),Se();var we=Rt(),he=_(we),N=u(he,2);const ue=x(()=>(S(de),r(()=>[de.Hover])));Te(N,{get content(){return ee()},get triggerOn(){return e(ue)},maxWidth:"400px",children:(se,Ae)=>{var fe=At(),V=_(fe);ae(V,{size:1,color:"secondary",children:(le,Ve)=>{var Pe=ne();M(()=>ce(Pe,ee())),n(le,Pe)},$$slots:{default:!0}}),n(se,fe)},$$slots:{default:!0}});var te=u(N,2),Fe=se=>{var Ae=Pt(),fe=_(Ae);const V=x(()=>b()==="error"?"error":"neutral");ae(fe,{size:1,get color(){return e(V)},children:(le,Ve)=>{var Pe=ne();M(()=>ce(Pe,b()==="error"?"!":b()==="warning"?"⚠":"")),n(le,Pe)},$$slots:{default:!0}}),n(se,Ae)};O(te,se=>{b()!=="error"&&b()!=="warning"||se(Fe)}),M(()=>Ge(he,1,`bullet-point ${e(w)??""}`,"svelte-1tatwxk")),n(t,we),ke()})(g,{get text(){return e(s)},status:"success"})}),n(a,o)};O(ge,a=>{e(P),r(()=>e(P).length>0)&&a(be)});var pe=u(oe,2),xe=_(pe);const De=x(()=>K()?"Unpin agent":"Pin agent"),Re=x(()=>(S(de),r(()=>[de.Hover])));Te(xe,{get content(){return e(De)},get triggerOn(){return e(Re)},side:"top",children:(a,o)=>{Be(a,{variant:"ghost",color:"neutral",size:1,$$events:{click:g=>{g.stopPropagation(),L()()}},children:(g,s)=>{var t=et(),f=G(t),w=b=>{(function(we,he){const N=Ye(he,["children","$$slots","$$events","$$legacy"]);var ue=kt();Qe(ue,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 640 512",...N}));var te=_(ue);it(te,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M38.8 5.1C28.4-3.1 13.3-1.2 5.1 9.2s-6.3 25.5 4.1 33.7l592 464c10.4 8.2 25.5 6.3 33.7-4.1s6.3-25.5-4.1-33.7L481.4 352c9.4-.4 18.1-4.9 23.9-12.3 6.1-7.8 8.2-17.9 5.8-27.5l-6.2-25c-10.3-41.3-35.4-75.7-68.7-98.3L428.9 96l-3.7-48H456c4.4 0 8.6-1.2 12.2-3.3 7-4.2 11.8-11.9 11.8-20.7 0-13.3-10.7-24-24-24H184c-13.3 0-24 10.7-24 24 0 8.8 4.8 16.5 11.8 20.7 3.6 2.1 7.7 3.3 12.2 3.3h30.8l-3.7 48-3.2 41.6zm214.5 168.1 9.3-121.5c.1-1.2.1-2.5.1-3.7h114.5c0 1.2 0 2.5.1 3.7l10.8 140.9c1.1 14.6 8.8 27.8 20.9 36 23.9 16.2 41.7 40.8 49.1 70.2l1.3 5.1H420l-76-59.6V216c0-13.3-10.7-24-24-24-10.4 0-19.2 6.6-22.6 15.8l-44.2-34.6zM344 367l-80-63h-83.5l1.3-5.1c4-16.1 11.2-30.7 20.7-43.3l-37.7-29.7c-13.7 17.8-23.9 38.6-29.6 61.4l-6.2 25c-2.4 9.6-.2 19.7 5.8 27.5s15.4 12.3 25.2 12.3h136v136c0 13.3 10.7 24 24 24s24-10.7 24-24v-121z"/>',!0),n(we,ue)})(b,{})},ee=b=>{vt(b,{})};O(f,b=>{K()?b(w):b(ee,!1)}),n(g,t)},$$slots:{default:!0}})},$$slots:{default:!0}});var ze=u(xe,2);const Ie=x(()=>(S(de),r(()=>[de.Hover])));Te(ze,{content:"SSH to agent",get triggerOn(){return e(Ie)},side:"top",children:(a,o)=>{const g=x(()=>!v()),s=x(()=>v()?"SSH to agent":"SSH to agent (agent must be running or idle)");Be(a,{get disabled(){return e(g)},variant:"ghost",color:"neutral",size:1,get title(){return e(s)},$$events:{click:t=>{U()&&(t.stopPropagation(),U()())}},children:(t,f)=>{Ke(t)},$$slots:{default:!0}})},$$slots:{default:!0}});var Ce=u(ze,2);const We=x(()=>(S(de),r(()=>[de.Hover])));Te(Ce,{content:"Delete agent",get triggerOn(){return e(We)},side:"top",children:(a,o)=>{const g=x(()=>H()?"Deleting agent...":"Delete agent");Be(a,{variant:"ghost",color:"neutral",size:1,get disabled(){return H()},get title(){return e(g)},$$events:{click:s=>{s.stopPropagation(),T()()}},children:(s,t)=>{gt(s)},$$slots:{default:!0}})},$$slots:{default:!0}});var Le=u(pe,2);const p=x(()=>(S(i()),r(()=>i().updated_at||i().started_at)));(function(a,o){ye(o,!1);let g=y(o,"isRemote",8,!1),s=y(o,"status",8),t=y(o,"timestamp",8),f=C($t(t()));const w=yt(t(),N=>{A(f,N)});Me(()=>{w()}),Se();var ee=xt(),b=_(ee);ae(b,{size:1,color:"secondary",class:"location-text",children:(N,ue)=>{var te=ne();M(()=>ce(te,g()?"Running in the cloud":"Running locally")),n(N,te)},$$slots:{default:!0}});var we=u(b,2),he=_(we);ae(he,{size:1,color:"secondary",class:"time-text",children:(N,ue)=>{var te=bt(),Fe=G(te),se=u(Fe),Ae=V=>{var le=ne();M(()=>ce(le,e(f))),n(V,le)},fe=V=>{var le=ne("Unknown time");n(V,le)};O(se,V=>{t()?V(Ae):V(fe,!1)}),M(()=>ce(Fe,`${S(s()),S(Y),r(()=>s()===Y.agentRunning?"Last updated":"Started")??""} `)),n(N,te)},$$slots:{default:!0}}),n(a,ee),ke()})(Le,{get isRemote(){return e(q)},get status(){return S(i()),r(()=>i().status)},get timestamp(){return e(p)}}),M(()=>tt(c,"title",(S(i()),r(()=>i().is_setup_script_agent?"Generate a setup script":i().session_summary)))),n(z,F)},$$slots:{default:!0}}),M(z=>d=Ge(E,1,"card-wrapper svelte-1bxdvw4",null,d,z),[()=>({"selected-card":J(),"setup-script-card":i().is_setup_script_agent,deleting:H()})],x),n(ve,E),ke()}function _e(ve,$){ye($,!1);const[P,q]=Ne(),i=()=>Xe(U,"$sharedWebviewStore",P),J=C(),K=C(),W=C();let T=y($,"agent",8),R=y($,"selected",8,!1),H=y($,"onSelect",8);const L=Ee(He.key),U=Ee(je);let v=C(!1),B=C(null),E=null;function d(){A(B,null),E&&(clearTimeout(E),E=null)}Z(()=>i(),()=>{var m;A(J,((m=i().state)==null?void 0:m.pinnedAgents)||{})}),Z(()=>(e(J),S(T())),()=>{var m;A(K,((m=e(J))==null?void 0:m[T().remote_agent_id])===!0)}),Z(()=>(S(T()),Y),()=>{A(W,T().status===Y.agentRunning||T().status===Y.agentIdle)}),qe(),Se(),Et(ve,{get agent(){return T()},get selected(){return R()},get isPinned(){return e(K)},get onSelect(){return H()},onDelete:()=>async function(m){var I,z;d(),A(v,!0);const Q=((I=i().state)==null?void 0:I.agentOverviews)||[];try{if(!await L.deleteRemoteAgent(m))throw new Error("Failed to delete agent");if(U.update(k=>{if(k)return{...k,agentOverviews:k.agentOverviews.filter(F=>F.remote_agent_id!==m)}}),(((z=i().state)==null?void 0:z.pinnedAgents)||{})[m])try{await L.deletePinnedAgentFromStore(m);const k=await L.getPinnedAgentsFromStore();U.update(F=>{if(F)return{...F,pinnedAgents:k}})}catch(k){console.error("Failed to remove pinned status:",k)}}catch(k){console.error("Failed to delete agent:",k),U.update(F=>{if(F)return{...F,agentOverviews:Q}}),A(B,k instanceof Error?k.message:"Failed to delete agent"),E=setTimeout(()=>{d()},5e3)}finally{A(v,!1)}}(T().remote_agent_id),onTogglePinned:()=>async function(m){try{e(K)?await L.deletePinnedAgentFromStore(m):await L.savePinnedAgentToStore(m,!0);const Q=await L.getPinnedAgentsFromStore();U.update(I=>{if(I)return{...I,pinnedAgents:Q}})}catch(Q){console.error("Failed to toggle pinned status:",Q)}}(T().remote_agent_id),onSSH:async function(){return!!e(W)&&await(async m=>await L.sshToRemoteAgent(m.remote_agent_id))(T())},get canSsh(){return e(W)},get deletionError(){return e(B)},set deletionError(m){A(B,m)},get isDeleting(){return e(v)},set isDeleting(m){A(v,m)},$$legacy:!0}),ke(),q()}var Dt=h('<div class="section-header svelte-1tegnqi"><!></div>');function $e(ve,$){let P=y($,"title",8);var q=Dt(),i=_(q);ae(i,{size:2,color:"secondary",children:(J,K)=>{var W=ne();M(()=>ce(W,P())),n(J,W)},$$slots:{default:!0}}),n(ve,q)}var Ct=h('<div class="empty-state svelte-aiqmvp"><div class="l-loading-container svelte-aiqmvp"><!> <!></div></div>'),Wt=h('<div class="empty-state svelte-aiqmvp"><!></div>'),Lt=h('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),Bt=h('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),jt=h('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),Mt=h('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),Gt=h('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),Jt=h('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),Kt=h("<!> <!> <!> <!> <!> <!>",1),Ut=h('<div class="agent-list svelte-aiqmvp"><!></div>'),Xt=h('<div class="l-main svelte-1941nw6"><h1 class="l-main__title svelte-1941nw6"><span class="l-main__title-logo svelte-1941nw6"><!></span> Remote Agents</h1> <!></div>');rt(function(ve,$){ye($,!1);const P=new dt(lt),q=new ut(P,void 0,mt,pt);P.registerConsumer(q),Je(je,q);const i=new He(P);Je(He.key,i),at(()=>(q.fetchStateFromExtension().then(()=>{q.update(R=>{if(!R)return;const H=[...R.activeWebviews,"home"];return R.pinnedAgents?{...R,activeWebviews:H}:{...R,activeWebviews:H,pinnedAgents:{}}})}),()=>{P.dispose(),i.dispose()})),Se();var J=Xt();Ue("message",nt,function(...R){var H;(H=P.onMessageFromExtension)==null||H.apply(this,R)});var K=_(J),W=_(K),T=_(W);St(T),function(R,H){ye(H,!1);const[L,U]=Ne(),v=()=>Xe(m,"$sharedWebviewStore",L),B=C(),E=C(),d=C(),m=Ee(je),Q=Ee(He.key);function I(c){m.update(l=>{if(l)return{...l,selectedAgentId:c}})}Z(()=>v(),()=>{var c;A(B,wt(((c=v().state)==null?void 0:c.agentOverviews)||[]))}),Z(()=>v(),()=>{var c;A(E,((c=v().state)==null?void 0:c.pinnedAgents)||{})}),Z(()=>(e(B),e(E),Oe),()=>{A(d,e(B).reduce((c,l)=>{var j;return((j=e(E))==null?void 0:j[l.remote_agent_id])===!0?c.pinned.push(l):l.status===Y.agentIdle&&l.has_updates?c.readyToReview.push(l):l.status===Y.agentRunning||l.status===Y.agentStarting||l.workspace_status===Oe.workspaceResuming?c.running.push(l):l.status===Y.agentFailed?c.failed.push(l):l.status===Y.agentIdle||l.workspace_status===Oe.workspacePaused||l.workspace_status===Oe.workspacePausing?c.idle.push(l):c.additional.push(l),c},{pinned:[],readyToReview:[],running:[],idle:[],failed:[],additional:[]}))}),Z(()=>v(),()=>{var c;(c=v().state)!=null&&c.agentOverviews||Q.focusAugmentPanel()}),qe(),Se();var z=Ut(),k=_(z),F=c=>{var l=Ct(),j=_(l),ie=_(j);st(ie,{});var X=u(ie,2);ae(X,{size:3,color:"secondary",children:(D,oe)=>{var ge=ne("Loading the Augment panel...");n(D,ge)},$$slots:{default:!0}}),n(c,l)},re=(c,l)=>{var j=X=>{var D=Wt(),oe=_(D);ae(oe,{size:3,color:"secondary",children:(ge,be)=>{var pe=ne("No agents available");n(ge,pe)},$$slots:{default:!0}}),n(X,D)},ie=X=>{var D=Kt(),oe=G(D),ge=p=>{var a=Lt(),o=G(a);$e(o,{title:"Pinned"});var g=u(o,2);me(g,7,()=>(e(d),r(()=>e(d).pinned)),(s,t)=>s.remote_agent_id+t,(s,t)=>{const f=x(()=>(e(t),v(),r(()=>{var w;return e(t).remote_agent_id===((w=v().state)==null?void 0:w.selectedAgentId)})));_e(s,{get agent(){return e(t)},get selected(){return e(f)},onSelect:I})}),n(p,a)};O(oe,p=>{e(d),r(()=>e(d).pinned.length>0)&&p(ge)});var be=u(oe,2),pe=p=>{var a=Bt(),o=G(a);$e(o,{title:"Ready to review"});var g=u(o,2);me(g,7,()=>(e(d),r(()=>e(d).readyToReview)),(s,t)=>s.remote_agent_id+t,(s,t)=>{const f=x(()=>(e(t),v(),r(()=>{var w;return e(t).remote_agent_id===((w=v().state)==null?void 0:w.selectedAgentId)})));_e(s,{get agent(){return e(t)},get selected(){return e(f)},onSelect:I})}),n(p,a)};O(be,p=>{e(d),r(()=>e(d).readyToReview.length>0)&&p(pe)});var xe=u(be,2),De=p=>{var a=jt(),o=G(a);$e(o,{title:"Running agents"});var g=u(o,2);me(g,7,()=>(e(d),r(()=>e(d).running)),(s,t)=>s.remote_agent_id+t,(s,t)=>{const f=x(()=>(e(t),v(),r(()=>{var w;return e(t).remote_agent_id===((w=v().state)==null?void 0:w.selectedAgentId)})));_e(s,{get agent(){return e(t)},get selected(){return e(f)},onSelect:I})}),n(p,a)};O(xe,p=>{e(d),r(()=>e(d).running.length>0)&&p(De)});var Re=u(xe,2),ze=p=>{var a=Mt(),o=G(a);$e(o,{title:"Idle agents"});var g=u(o,2);me(g,7,()=>(e(d),r(()=>e(d).idle)),(s,t)=>s.remote_agent_id+t,(s,t)=>{const f=x(()=>(e(t),v(),r(()=>{var w;return e(t).remote_agent_id===((w=v().state)==null?void 0:w.selectedAgentId)})));_e(s,{get agent(){return e(t)},get selected(){return e(f)},onSelect:I})}),n(p,a)};O(Re,p=>{e(d),r(()=>e(d).idle.length>0)&&p(ze)});var Ie=u(Re,2),Ce=p=>{var a=Gt(),o=G(a);$e(o,{title:"Failed agents"});var g=u(o,2);me(g,7,()=>(e(d),r(()=>e(d).failed)),(s,t)=>s.remote_agent_id+t,(s,t)=>{const f=x(()=>(e(t),v(),r(()=>{var w;return e(t).remote_agent_id===((w=v().state)==null?void 0:w.selectedAgentId)})));_e(s,{get agent(){return e(t)},get selected(){return e(f)},onSelect:I})}),n(p,a)};O(Ie,p=>{e(d),r(()=>e(d).failed.length>0)&&p(Ce)});var We=u(Ie,2),Le=p=>{var a=Jt(),o=G(a);$e(o,{title:"Other agents"});var g=u(o,2);me(g,7,()=>(e(d),r(()=>e(d).additional)),(s,t)=>s.remote_agent_id+t,(s,t)=>{const f=x(()=>(e(t),v(),r(()=>{var w;return e(t).remote_agent_id===((w=v().state)==null?void 0:w.selectedAgentId)})));_e(s,{get agent(){return e(t)},get selected(){return e(f)},onSelect:I})}),n(p,a)};O(We,p=>{e(d),r(()=>e(d).additional.length>0)&&p(Le)}),n(X,D)};O(c,X=>{v(),r(()=>{var D;return((D=v().state)==null?void 0:D.agentOverviews.length)===0})?X(j):X(ie,!1)},l)};O(k,c=>{v(),r(()=>{var l;return!((l=v().state)!=null&&l.agentOverviews)})?c(F):c(re,!1)}),n(R,z),ke(),U()}(u(K,2),{}),n(ve,J),ke()},{target:document.getElementById("app")});
