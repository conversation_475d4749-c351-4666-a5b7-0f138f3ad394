<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Augment Smart Diff View</title>
    <script nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <script type="module" crossorigin src="./assets/diff-view-ChFEvfZN.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-kH3m-zOb.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-IgXUmngh.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-C8Qb_O9b.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/async-messaging-CCLqHBoR.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-BAmr_-U4.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/message-broker-CwcPcQ_e.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/types-CGlLNakm.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/file-paths-Bl_IgYJd.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/BaseTextInput-TeF8u93x.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-BnlWKkvq.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/folder-opened-D9klsFkp.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/monaco-render-utils-DfwV7QLY.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-BZfc2Zk1.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/CalloutAugment-BUpCn_TI.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/exclamation-triangle-C_LrR9gu.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-D-fDrvnq.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/svelte-component-B6upIdsM.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/Filespan-B9x3U15q.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/MaterialIcon-Cs9y5rDt.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/pen-to-square-CYPFKWo4.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/augment-logo-C7lllHv9.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/Keybindings-uAvjYLcu.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/chat-context-nrEZUHNl.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-B_qLJJaQ.js" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DoxdFmoV.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-DgOX1UWm.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-B4afvB2A.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-DRIZURB3.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/folder-opened-hTsrGIsd.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/index-D9au8v71.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-BcSV_kHI.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/Keybindings-D61-5dMN.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/index-BlHvDt2c.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/BaseTextInput-CEzLOEg8.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/CalloutAugment-C-z-uXWx.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/Filespan-CMEPAZfs.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/MaterialIcon-BO_oU5T3.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
    <link rel="stylesheet" crossorigin href="./assets/diff-view-gNBD1MuH.css" nonce="nonce-Wwx4atWoD92yaGPKM8YUVQ==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
